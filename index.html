<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Primeval Quiz</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- <PERSON><PERSON>ran d'accueil -->
    <div id="welcome-screen" class="screen active">
        <div class="background-overlay"></div>
        <div class="welcome-content">
            <div class="logo-container">
                <h1 class="game-title">PRIMEVAL QUIZ</h1>
                <p class="game-subtitle">Testez vos connaissances</p>
            </div>
            
            <div class="difficulty-selection">
                <h2>Choisissez votre niveau de difficulté</h2>
                <div class="difficulty-buttons">
                    <button class="difficulty-btn" data-difficulty="facile">
                        <i class="fas fa-seedling"></i>
                        <span>Facile</span>
                    </button>
                    <button class="difficulty-btn" data-difficulty="intermediaire">
                        <i class="fas fa-tree"></i>
                        <span>Intermédiaire</span>
                    </button>
                    <button class="difficulty-btn" data-difficulty="difficile">
                        <i class="fas fa-mountain"></i>
                        <span>Difficile</span>
                    </button>
                </div>
            </div>
            
            <button id="start-game-btn" class="main-btn" disabled>
                <i class="fas fa-play"></i>
                Commencer
            </button>
        </div>
    </div>

    <!-- Menu principal -->
    <div id="main-menu" class="screen">
        <div class="background-overlay"></div>
        <div class="menu-content">
            <div class="header">
                <h1 class="game-title">PRIMEVAL QUIZ</h1>
                <div class="user-stats">
                    <div class="level-indicator">
                        <span id="current-level">Primaire</span>
                    </div>
                </div>
            </div>

            <div class="menu-grid">
                <div class="menu-section">
                    <h2>Modes de jeu</h2>
                    <div class="menu-buttons">
                        <button class="menu-btn" id="solo-mode-btn">
                            <i class="fas fa-user"></i>
                            <span>Mode Solo</span>
                            <small>Progression par niveaux</small>
                        </button>
                        <button class="menu-btn" id="multiplayer-mode-btn">
                            <i class="fas fa-users"></i>
                            <span>Multijoueur</span>
                            <small>Défis en temps réel</small>
                        </button>
                        <button class="menu-btn" id="daily-challenge-btn">
                            <i class="fas fa-calendar-day"></i>
                            <span>Énigme du jour</span>
                            <small>Récompenses exclusives</small>
                        </button>
                    </div>
                </div>

                <div class="menu-section">
                    <h2>Thèmes</h2>
                    <div class="theme-grid">
                        <button class="theme-btn" data-theme="mathematiques">
                            <i class="fas fa-calculator"></i>
                            <span>Mathématiques</span>
                        </button>
                        <button class="theme-btn" data-theme="histoire">
                            <i class="fas fa-landmark"></i>
                            <span>Histoire</span>
                        </button>
                        <button class="theme-btn" data-theme="physique">
                            <i class="fas fa-atom"></i>
                            <span>Physique</span>
                        </button>
                        <button class="theme-btn" data-theme="geographie">
                            <i class="fas fa-globe"></i>
                            <span>Géographie</span>
                        </button>
                        <button class="theme-btn" data-theme="arts">
                            <i class="fas fa-palette"></i>
                            <span>Arts & Culture</span>
                        </button>
                        <button class="theme-btn" data-theme="personnalites">
                            <i class="fas fa-user-tie"></i>
                            <span>Personnalités</span>
                        </button>
                    </div>
                </div>
            </div>

            <div class="bottom-menu">
                <button class="icon-btn" id="settings-btn">
                    <i class="fas fa-cog"></i>
                    <span>Paramètres</span>
                </button>
                <button class="icon-btn" id="rankings-btn">
                    <i class="fas fa-trophy"></i>
                    <span>Classements</span>
                </button>
                <button class="icon-btn" id="credits-btn">
                    <i class="fas fa-info-circle"></i>
                    <span>Crédits</span>
                </button>
            </div>
        </div>

        <!-- Classement latéral -->
        <div class="sidebar-ranking">
            <h3>Classement Solo</h3>
            <div class="ranking-stats">
                <div class="stat-item">
                    <span class="stat-label">Vitesse</span>
                    <span class="stat-value" id="speed-rank">-</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Progression</span>
                    <span class="stat-value" id="progress-rank">-</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Hebdomadaire</span>
                    <span class="stat-value" id="weekly-rank">-</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Écran de jeu/quiz -->
    <div id="game-screen" class="screen">
        <div class="background-overlay"></div>
        <div class="game-content">
            <div class="game-header">
                <div class="game-info">
                    <span class="theme-name" id="current-theme">Mathématiques</span>
                    <span class="level-name" id="current-game-level">Primaire - Facile</span>
                </div>
                <div class="game-stats">
                    <div class="timer">
                        <i class="fas fa-clock"></i>
                        <span id="timer-display">02:00</span>
                    </div>
                    <div class="score">
                        <i class="fas fa-star"></i>
                        <span id="score-display">0</span>
                    </div>
                    <div class="errors">
                        <i class="fas fa-times"></i>
                        <span id="errors-display">0/3</span>
                    </div>
                </div>
            </div>

            <div class="question-container">
                <div class="question-number">
                    Question <span id="question-num">1</span> / <span id="total-questions">10</span>
                </div>
                <div class="question-text" id="question-text">
                    Quelle est la capitale de la France ?
                </div>
                <div class="question-image" id="question-image" style="display: none;">
                    <img src="" alt="Question image" id="question-img">
                </div>
            </div>

            <div class="answers-container" id="answers-container">
                <!-- Les réponses seront générées dynamiquement -->
            </div>

            <div class="game-controls">
                <button class="control-btn" id="hint-btn">
                    <i class="fas fa-lightbulb"></i>
                    Indice
                </button>
                <button class="control-btn" id="skip-btn">
                    <i class="fas fa-forward"></i>
                    Passer
                </button>
                <button class="control-btn" id="quit-game-btn">
                    <i class="fas fa-times"></i>
                    Quitter
                </button>
            </div>
        </div>
    </div>

    <!-- Écran des paramètres -->
    <div id="settings-screen" class="screen">
        <div class="background-overlay"></div>
        <div class="settings-content">
            <div class="settings-header">
                <button class="back-btn" id="settings-back-btn">
                    <i class="fas fa-arrow-left"></i>
                </button>
                <h2>Paramètres</h2>
            </div>

            <div class="settings-sections">
                <div class="settings-section">
                    <h3>Audio</h3>
                    <div class="setting-item">
                        <label for="music-volume">Volume musique</label>
                        <input type="range" id="music-volume" min="0" max="100" value="70">
                        <span class="volume-value">70%</span>
                    </div>
                    <div class="setting-item">
                        <label for="sfx-volume">Volume effets</label>
                        <input type="range" id="sfx-volume" min="0" max="100" value="80">
                        <span class="volume-value">80%</span>
                    </div>
                </div>

                <div class="settings-section">
                    <h3>Jeu</h3>
                    <div class="setting-item">
                        <label for="language-select">Langue</label>
                        <select id="language-select">
                            <option value="fr">Français</option>
                            <option value="en">English</option>
                            <option value="es">Español</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <label for="default-difficulty">Difficulté par défaut</label>
                        <select id="default-difficulty">
                            <option value="facile">Facile</option>
                            <option value="intermediaire">Intermédiaire</option>
                            <option value="difficile">Difficile</option>
                        </select>
                    </div>
                </div>

                <div class="settings-section">
                    <h3>Accessibilité</h3>
                    <div class="setting-item">
                        <label for="colorblind-mode">Mode daltonien</label>
                        <input type="checkbox" id="colorblind-mode">
                    </div>
                    <div class="setting-item">
                        <label for="high-contrast">Contraste élevé</label>
                        <input type="checkbox" id="high-contrast">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Écran des classements -->
    <div id="rankings-screen" class="screen">
        <div class="background-overlay"></div>
        <div class="rankings-content">
            <div class="rankings-header">
                <button class="back-btn" id="rankings-back-btn">
                    <i class="fas fa-arrow-left"></i>
                </button>
                <h2>Classements</h2>
            </div>

            <div class="rankings-tabs">
                <button class="tab-btn active" data-tab="solo">Solo</button>
                <button class="tab-btn" data-tab="multiplayer">Multijoueur</button>
                <button class="tab-btn" data-tab="global">Global</button>
            </div>

            <div class="rankings-content-area">
                <div class="tab-content active" id="solo-rankings">
                    <!-- Contenu des classements solo -->
                </div>
                <div class="tab-content" id="multiplayer-rankings">
                    <!-- Contenu des classements multijoueur -->
                </div>
                <div class="tab-content" id="global-rankings">
                    <!-- Contenu des classements globaux -->
                </div>
            </div>
        </div>
    </div>

    <!-- Écran des crédits -->
    <div id="credits-screen" class="screen">
        <div class="background-overlay"></div>
        <div class="credits-content">
            <div class="credits-header">
                <button class="back-btn" id="credits-back-btn">
                    <i class="fas fa-arrow-left"></i>
                </button>
                <h2>Crédits</h2>
            </div>

            <div class="credits-info">
                <div class="badge-honor">
                    <i class="fas fa-medal"></i>
                    <h3>Badge d'honneur</h3>
                    <p>Créateurs du jeu Primeval Quiz</p>
                </div>
                
                <div class="team-info">
                    <h4>Équipe de développement</h4>
                    <p>Développé avec passion pour l'apprentissage</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Modales -->
    <div id="modal-overlay" class="modal-overlay">
        <div class="modal" id="result-modal">
            <div class="modal-content">
                <h3 id="result-title">Résultat</h3>
                <p id="result-message">Message</p>
                <div class="modal-buttons">
                    <button class="modal-btn" id="continue-btn">Continuer</button>
                    <button class="modal-btn secondary" id="menu-btn">Menu</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Audio -->
    <audio id="background-music" loop preload="auto">
        <source src="audio/electro-background.mp3" type="audio/mpeg">
        <source src="audio/electro-background.ogg" type="audio/ogg">
        <source src="audio/electro-background.wav" type="audio/wav">
    </audio>
    <audio id="menu-music" loop preload="auto">
        <source src="audio/menu-electro.mp3" type="audio/mpeg">
        <source src="audio/menu-electro.ogg" type="audio/ogg">
    </audio>
    <audio id="game-music" loop preload="auto">
        <source src="audio/game-electro.mp3" type="audio/mpeg">
        <source src="audio/game-electro.ogg" type="audio/ogg">
    </audio>
    <audio id="correct-sound" preload="auto">
        <source src="audio/correct.mp3" type="audio/mpeg">
        <source src="audio/correct.ogg" type="audio/ogg">
    </audio>
    <audio id="wrong-sound" preload="auto">
        <source src="audio/wrong.mp3" type="audio/mpeg">
        <source src="audio/wrong.ogg" type="audio/ogg">
    </audio>
    <audio id="victory-sound" preload="auto">
        <source src="audio/victory.mp3" type="audio/mpeg">
        <source src="audio/victory.ogg" type="audio/ogg">
    </audio>
    <audio id="level-up-sound" preload="auto">
        <source src="audio/level-up.mp3" type="audio/mpeg">
        <source src="audio/level-up.ogg" type="audio/ogg">
    </audio>

    <script src="config.js"></script>
    <script src="script.js"></script>
</body>
</html>
