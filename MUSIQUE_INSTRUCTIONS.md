# 🎵 Instructions pour ajouter la musique électro au jeu

## 📁 Fichiers audio nécessaires

Le jeu est configuré pour utiliser les fichiers audio suivants dans le dossier `audio/` :

### 🎶 Musiques de fond (électro, sans paroles)
1. **`electro-background.mp3`** - Musique d'accueil (2-3 minutes, loop)
2. **`menu-electro.mp3`** - Musique du menu principal (2-3 minutes, loop)  
3. **`game-electro.mp3`** - Musique pendant le jeu (3-4 minutes, loop)

### 🔊 Effets sonores
4. **`correct.mp3`** - Son de bonne réponse (0.5-1 seconde)
5. **`wrong.mp3`** - Son de mauvaise réponse (0.5-1 seconde)
6. **`victory.mp3`** - Son de victoire (2-3 secondes)
7. **`level-up.mp3`** - Son de passage de niveau (2-3 secondes)

## 🎵 Caractéristiques recommandées pour la musique électro

### Style musical
- **Genre** : Électro, synthwave, chillwave, ou ambient électronique
- **Tempo** : 120-140 BPM pour l'énergie, 80-100 BPM pour l'ambiance
- **Ambiance** : Entrainante mais pas trop agressive, propice à la concentration
- **Instruments** : Synthétiseurs, pads, arpeggios, beats électroniques

### Spécifications techniques
- **Format** : MP3 (320 kbps) ou OGG Vorbis
- **Durée** : 2-4 minutes (le jeu fait des loops automatiques)
- **Volume** : Normalisé, pas de pics trop forts
- **Fade** : Début et fin en fade pour des loops parfaits

## 🌐 Sources recommandées pour obtenir la musique

### Sites de musique libre de droits
1. **Freesound.org** - Musiques et sons gratuits
2. **Zapsplat.com** - Bibliothèque audio (inscription gratuite)
3. **YouTube Audio Library** - Musiques libres de YouTube
4. **Incompetech.com** - Musiques de Kevin MacLeod (Creative Commons)
5. **Pixabay Music** - Musiques gratuites
6. **Jamendo** - Musiques Creative Commons

### Mots-clés de recherche
- "electro background music"
- "synthwave instrumental"
- "electronic ambient"
- "chillwave loop"
- "retro electronic"
- "cyberpunk ambient"
- "game background music electro"

### Générateurs d'IA musicale (gratuits)
1. **AIVA** - Générateur de musique IA
2. **Amper Music** - Création musicale automatique
3. **Soundraw** - Générateur de musique personnalisée
4. **Boomy** - Création rapide de musique électro

## 🛠️ Comment ajouter les fichiers

1. **Téléchargez** les fichiers audio selon les spécifications ci-dessus
2. **Renommez** les fichiers selon la liste ci-dessus
3. **Placez** tous les fichiers dans le dossier `audio/`
4. **Testez** le jeu - la musique devrait se lancer automatiquement

## 🎚️ Configuration dans le jeu

Le jeu gère automatiquement :
- ✅ **Changement de musique** selon l'écran (accueil, menu, jeu)
- ✅ **Transitions fluides** avec fade in/out
- ✅ **Contrôle du volume** via les paramètres
- ✅ **Loops automatiques** pour la musique de fond
- ✅ **Gestion des erreurs** si les fichiers sont manquants

## 🎵 Exemple de structure de musique électro idéale

### Musique d'accueil (electro-background.mp3)
- **0-15s** : Intro douce avec pads
- **15s-1m30** : Mélodie principale avec arpeggios
- **1m30-2m** : Variation avec plus d'énergie
- **2m-2m15** : Outro qui se connecte bien au début (loop)

### Musique de jeu (game-electro.mp3)
- **Rythme constant** pour maintenir la concentration
- **Éléments motivants** sans être distrayants
- **Pas de drops trop intenses** qui pourraient surprendre
- **Ambiance futuriste** en accord avec le thème du jeu

## 🔧 Formats alternatifs supportés

Le jeu supporte plusieurs formats pour une meilleure compatibilité :
- **MP3** (recommandé) - Compatible partout
- **OGG** - Meilleure qualité, plus petit
- **WAV** - Qualité maximale (plus lourd)

## 🎮 Test de la musique

Une fois les fichiers ajoutés :
1. Ouvrez `index.html` dans votre navigateur
2. Cliquez sur un bouton pour activer l'audio
3. La musique d'accueil devrait se lancer
4. Naviguez entre les écrans pour tester les transitions
5. Ajustez le volume dans les paramètres

## 🚨 Dépannage

### La musique ne se lance pas
- Vérifiez que les fichiers sont dans le bon dossier
- Vérifiez les noms de fichiers (sensible à la casse)
- Ouvrez la console du navigateur (F12) pour voir les erreurs
- Certains navigateurs bloquent l'autoplay - cliquez d'abord sur un bouton

### La musique est trop forte/faible
- Utilisez les paramètres du jeu pour ajuster le volume
- Normalisez vos fichiers audio avant de les ajouter

### Les transitions ne sont pas fluides
- Vérifiez que vos fichiers ont des fade in/out
- Assurez-vous que les loops sont parfaits (début = fin)

---

**🎵 Une fois la musique ajoutée, votre jeu Primeval Quiz aura une ambiance électro entrainante parfaite pour motiver les joueurs !**
