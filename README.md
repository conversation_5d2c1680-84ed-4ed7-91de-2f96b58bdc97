# Primeval Quiz

Un jeu de quiz interactif et éducatif avec des questions de culture générale sur différents thèmes et niveaux de difficulté.

## 🎮 Fonctionnalités Principales

### Thèmes de Culture Générale
- **Mathématiques** : Calculs rapides, algèbre, problèmes logiques
- **Histoire** : Événements historiques, dates clés, personnalités
- **Physique** : Lois fond<PERSON>, expériences scientifiques, inventions
- **Géographie** : Capitales, pays, départements
- **Arts et Culture** : Œuvres célèbres, artistes, cinéma, littérature
- **Personnalités** : Identifier des figures historiques, politiques ou culturelles

### Modes de Jeu

#### Mode Solo
- Progression à travers des niveaux scolaires (Primaire, Collège, Lycée, Expert)
- Chaque niveau inclut plusieurs étapes à difficulté croissante (Facile, Intermédiaire, Difficile)
- Énigmes quotidiennes pour obtenir des récompenses exclusives
- Classement solo intégré basé sur :
  - La vitesse de réalisation
  - Le plus haut niveau atteint
  - Le temps de jeu hebdomadaire

#### Mode Multijoueur (À venir)
- Défis en temps réel (1vs1)
- Système de points pour grimper dans un classement multijoueur mondial

### Modes Spéciaux
- **Reconnaissance visuelle** : Associer images et réponses (monuments, portraits, personnalités)
- **RANKED** : Classements compétitifs par mode et classement global toutes catégories confondues

## 🏆 Système de Classements

### Solo
- **Vitesse** : Basé sur le temps moyen de réponse
- **Progression** : Basé sur le pourcentage de bonnes réponses
- **Hebdomadaire** : Scores de la semaine en cours

### Multijoueur
- Basé sur les victoires en 1vs1

## ⚙️ Paramètres et Accessibilité

### Menu Paramètres
- Régler le volume de la musique et des effets sonores
- Choisir la langue du jeu
- Changer le niveau de difficulté par défaut
- Mode daltonien : amélioration des contrastes et des couleurs
- Mode contraste élevé pour une meilleure lisibilité

### Fonctionnalités d'Accessibilité
- Interface intuitive et colorée, mais épurée
- Classement affiché sur le côté de l'écran pour suivre sa progression
- Musique de fond permanente pour accompagner les parties

## 🎯 Règles du Jeu

### Mode Solo
- Répondre à toutes les questions dans le temps imparti (2 minutes par défaut)
- 2 types de réponses possibles :
  - Choix multiple
  - Saisie directe (texte ou chiffres)
- **Perte du niveau si** :
  - Le temps est écoulé
  - Plus de 3 erreurs

### Mode Multijoueur
- Défis rapides contre d'autres joueurs
- Victoire : plus de points ou rapidité supérieure

### Progression
- **Niveaux de difficulté** : Facile → Intermédiaire → Difficile
- **Niveaux scolaires** : Primaire → Collège → Lycée → Expert
- Déblocage automatique des niveaux supérieurs après 3 scores consécutifs ≥ 70%

## 🏅 Système d'Achievements

- **Premier pas** : Terminer votre première partie
- **Parfait !** : Obtenir 100% à un quiz
- **Éclair** : Terminer un quiz en moins de 60 secondes
- **Persévérant** : Jouer 10 parties

## 🚀 Installation et Utilisation

1. Téléchargez tous les fichiers du projet
2. Ouvrez `index.html` dans votre navigateur web
3. Sélectionnez votre niveau de difficulté initial
4. Choisissez un thème et commencez à jouer !

## 📁 Structure du Projet

```
PRIMEVAL QUIZ/
├── index.html          # Page principale du jeu
├── styles.css          # Styles et animations
├── script.js           # Logique du jeu et interactions
├── audio/              # Fichiers audio (musique et effets)
├── images/             # Images pour la reconnaissance visuelle
└── README.md           # Ce fichier
```

## 🎨 Design et Expérience Utilisateur

### Écran d'Ouverture
- Choix du niveau de difficulté (Facile, Intermédiaire, Difficile)
- Ce choix ne s'affiche qu'à la première utilisation et reste modifiable via les paramètres

### Interface
- Arrière-plan visuel : paysages variés selon les thèmes
- Interface intuitive et colorée, mais épurée
- Classement affiché sur le côté de l'écran pour suivre sa progression
- Animations fluides et feedback visuel

## 💰 Modèle de Monétisation (Concept)

- **Gratuit** avec achats in-app
- 1 énigme quotidienne gratuite
- **Abonnement à 1,99 €/mois** : jusqu'à 3 énigmes supplémentaires par jour
- Publicités optionnelles pour rejouer ou obtenir un indice

## 🏆 Crédits

Page spéciale dans le menu avec badge d'honneur pour les créateurs du jeu.

## 🔧 Technologies Utilisées

- **HTML5** : Structure de l'application
- **CSS3** : Styles, animations et responsive design
- **JavaScript ES6+** : Logique du jeu et interactions
- **LocalStorage** : Sauvegarde des données utilisateur
- **Font Awesome** : Icônes
- **Google Fonts** : Typographie (Poppins)

## 📱 Compatibilité

- Compatible avec tous les navigateurs modernes
- Design responsive pour desktop, tablette et mobile
- Optimisé pour les écrans tactiles

## 🔄 Mises à Jour Futures

- Mode multijoueur en temps réel
- Plus de thèmes et questions
- Système de badges avancé
- Intégration avec des API éducatives
- Mode hors ligne complet

---

**Développé avec passion pour l'apprentissage et le divertissement éducatif !** 🎓✨
