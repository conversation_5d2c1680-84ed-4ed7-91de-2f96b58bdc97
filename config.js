// Configuration du jeu Primeval Quiz
const GAME_CONFIG = {
    // Paramètres généraux
    GAME_NAME: "Primeval Quiz",
    VERSION: "1.0.0",
    
    // Paramètres de jeu
    DEFAULT_TIME_LIMIT: 120, // 2 minutes en secondes
    MAX_ERRORS: 3,
    MAX_QUESTIONS_PER_QUIZ: 10,
    
    // Scores et progression
    PASSING_SCORE: 70, // Pourcentage requis pour passer au niveau suivant
    CONSECUTIVE_GAMES_REQUIRED: 3, // Nombre de parties consécutives réussies pour débloquer le niveau suivant
    
    // Classements
    MAX_SCORES_STORED: 100,
    MAX_RECENT_SCORES: 50,
    
    // Points pour le mode RANKED
    RANKED_BASE_POINTS: 25,
    RANKED_SPEED_BONUS_MULTIPLIER: 0.1,
    
    // Sauvegarde
    AUTO_SAVE_INTERVAL: 30000, // 30 secondes
    LOCAL_STORAGE_MAX_SIZE: 5000000, // 5MB
    
    // Interface utilisateur
    NOTIFICATION_DURATION: 4000, // 4 secondes
    FEEDBACK_DURATION: 1500, // 1.5 secondes
    MODAL_ANIMATION_DURATION: 500, // 0.5 secondes
    
    // Audio
    DEFAULT_MUSIC_VOLUME: 70,
    DEFAULT_SFX_VOLUME: 80,
    
    // Thèmes disponibles
    AVAILABLE_THEMES: [
        'mathematiques',
        'histoire',
        'geographie',
        'physique',
        'arts',
        'personnalites'
    ],
    
    // Niveaux disponibles
    AVAILABLE_LEVELS: [
        'primaire',
        'college',
        'lycee',
        'expert'
    ],
    
    // Difficultés disponibles
    AVAILABLE_DIFFICULTIES: [
        'facile',
        'intermediaire',
        'difficile'
    ],
    
    // Langues supportées
    SUPPORTED_LANGUAGES: [
        { code: 'fr', name: 'Français' },
        { code: 'en', name: 'English' },
        { code: 'es', name: 'Español' }
    ],
    
    // Couleurs du thème
    THEME_COLORS: {
        primary: '#4a90e2',
        secondary: '#7b68ee',
        accent: '#ff6b6b',
        success: '#51cf66',
        warning: '#ffd43b',
        error: '#ff6b6b'
    },
    
    // Configuration des achievements
    ACHIEVEMENTS: [
        {
            id: 'first_game',
            name: 'Premier pas',
            description: 'Terminer votre première partie',
            icon: 'fas fa-baby',
            condition: (stats) => stats.gamesPlayed >= 1
        },
        {
            id: 'perfect_score',
            name: 'Parfait !',
            description: 'Obtenir 100% à un quiz',
            icon: 'fas fa-star',
            condition: (stats) => stats.percentage === 100
        },
        {
            id: 'speed_demon',
            name: 'Éclair',
            description: 'Terminer un quiz en moins de 60 secondes',
            icon: 'fas fa-bolt',
            condition: (stats) => stats.timeSpent <= 60
        },
        {
            id: 'persistent',
            name: 'Persévérant',
            description: 'Jouer 10 parties',
            icon: 'fas fa-medal',
            condition: (stats) => stats.gamesPlayed >= 10
        },
        {
            id: 'scholar',
            name: 'Érudit',
            description: 'Atteindre le niveau Expert',
            icon: 'fas fa-graduation-cap',
            condition: (stats) => stats.level === 'expert'
        },
        {
            id: 'streak_master',
            name: 'Série gagnante',
            description: 'Réussir 5 quiz consécutifs avec plus de 80%',
            icon: 'fas fa-fire',
            condition: (stats) => stats.consecutiveWins >= 5
        }
    ],
    
    // Configuration du mode RANKED
    RANKED_TIERS: [
        { name: 'Bronze', divisions: 3, minPoints: 0, color: '#cd7f32' },
        { name: 'Argent', divisions: 3, minPoints: 1200, color: '#c0c0c0' },
        { name: 'Or', divisions: 3, minPoints: 1500, color: '#ffd700' },
        { name: 'Platine', divisions: 3, minPoints: 1800, color: '#e5e4e2' },
        { name: 'Diamant', divisions: 3, minPoints: 2100, color: '#b9f2ff' },
        { name: 'Maître', divisions: 1, minPoints: 2400, color: '#ff6b6b' },
        { name: 'Grand Maître', divisions: 1, minPoints: 2700, color: '#9c27b0' }
    ],
    
    // Messages d'encouragement
    ENCOURAGEMENT_MESSAGES: {
        excellent: [
            "Exceptionnel !",
            "Parfait !",
            "Incroyable !",
            "Magistral !"
        ],
        good: [
            "Très bien !",
            "Excellent travail !",
            "Bien joué !",
            "Continue comme ça !"
        ],
        average: [
            "Pas mal !",
            "Tu progresses !",
            "C'est un bon début !",
            "Continue tes efforts !"
        ],
        poor: [
            "Tu peux mieux faire !",
            "Ne te décourage pas !",
            "L'entraînement paie !",
            "Persévère !"
        ]
    },
    
    // Configuration des indices
    HINT_SYSTEM: {
        enabled: true,
        maxHintsPerGame: 3,
        hintPenalty: 5 // Points perdus pour utiliser un indice
    },
    
    // Configuration du mode multijoueur (pour future implémentation)
    MULTIPLAYER_CONFIG: {
        enabled: false,
        maxPlayers: 2,
        timeLimit: 60,
        questionCount: 5
    },
    
    // URLs et ressources externes
    EXTERNAL_RESOURCES: {
        fontAwesome: 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css',
        googleFonts: 'https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap'
    },
    
    // Configuration de développement
    DEBUG_MODE: false,
    ENABLE_CONSOLE_LOGS: true,
    ENABLE_ERROR_REPORTING: true,
    
    // Métadonnées
    AUTHOR: "Équipe Primeval Quiz",
    DESCRIPTION: "Un jeu de quiz éducatif et interactif",
    KEYWORDS: ["quiz", "éducation", "culture générale", "jeu"],
    
    // Fonctions utilitaires
    getRandomEncouragementMessage: function(category) {
        const messages = this.ENCOURAGEMENT_MESSAGES[category] || this.ENCOURAGEMENT_MESSAGES.average;
        return messages[Math.floor(Math.random() * messages.length)];
    },
    
    isFeatureEnabled: function(feature) {
        switch (feature) {
            case 'multiplayer':
                return this.MULTIPLAYER_CONFIG.enabled;
            case 'hints':
                return this.HINT_SYSTEM.enabled;
            case 'debug':
                return this.DEBUG_MODE;
            default:
                return false;
        }
    },
    
    getThemeColor: function(colorName) {
        return this.THEME_COLORS[colorName] || this.THEME_COLORS.primary;
    }
};

// Exporter la configuration pour utilisation dans d'autres fichiers
if (typeof module !== 'undefined' && module.exports) {
    module.exports = GAME_CONFIG;
}

// Rendre la configuration disponible globalement
window.GAME_CONFIG = GAME_CONFIG;
