// État global du jeu
class GameState {
    constructor() {
        this.currentScreen = 'welcome-screen';
        this.selectedDifficulty = null;
        this.currentTheme = null;
        this.currentLevel = 'primaire';
        this.currentQuestionIndex = 0;
        this.score = 0;
        this.errors = 0;
        this.timeRemaining = 120; // 2 minutes par défaut
        this.timer = null;
        this.questions = [];
        this.userAnswers = [];
        this.settings = {
            musicVolume: 70,
            sfxVolume: 80,
            language: 'fr',
            defaultDifficulty: 'facile',
            colorblindMode: false,
            highContrast: false
        };
        this.rankings = {
            solo: {
                speed: [],
                progress: [],
                weekly: []
            },
            multiplayer: [],
            global: []
        };
        this.loadSettings();
        this.loadRankings();
    }

    loadSettings() {
        const saved = localStorage.getItem('primevalQuizSettings');
        if (saved) {
            this.settings = { ...this.settings, ...JSON.parse(saved) };
        }
    }

    saveSettings() {
        localStorage.setItem('primevalQuizSettings', JSON.stringify(this.settings));
    }

    loadRankings() {
        const saved = localStorage.getItem('primevalQuizRankings');
        if (saved) {
            this.rankings = { ...this.rankings, ...JSON.parse(saved) };
        }
    }

    saveRankings() {
        localStorage.setItem('primevalQuizRankings', JSON.stringify(this.rankings));
    }

    resetGame() {
        this.currentQuestionIndex = 0;
        this.score = 0;
        this.errors = 0;
        this.timeRemaining = 120;
        this.questions = [];
        this.userAnswers = [];
        if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
        }
    }
}

// Instance globale du jeu
const gameState = new GameState();

// Gestion des écrans
class ScreenManager {
    static showScreen(screenId) {
        // Masquer tous les écrans
        document.querySelectorAll('.screen').forEach(screen => {
            screen.classList.remove('active');
        });
        
        // Afficher l'écran demandé
        const targetScreen = document.getElementById(screenId);
        if (targetScreen) {
            targetScreen.classList.add('active');
            gameState.currentScreen = screenId;
        }
    }

    static showModal(modalId) {
        const overlay = document.getElementById('modal-overlay');
        const modal = document.getElementById(modalId);
        if (overlay && modal) {
            overlay.classList.add('active');
        }
    }

    static hideModal() {
        const overlay = document.getElementById('modal-overlay');
        if (overlay) {
            overlay.classList.remove('active');
        }
    }
}

// Gestion audio
class AudioManager {
    constructor() {
        this.backgroundMusic = document.getElementById('background-music');
        this.correctSound = document.getElementById('correct-sound');
        this.wrongSound = document.getElementById('wrong-sound');
        this.updateVolumes();
    }

    updateVolumes() {
        if (this.backgroundMusic) {
            this.backgroundMusic.volume = gameState.settings.musicVolume / 100;
        }
        if (this.correctSound) {
            this.correctSound.volume = gameState.settings.sfxVolume / 100;
        }
        if (this.wrongSound) {
            this.wrongSound.volume = gameState.settings.sfxVolume / 100;
        }
    }

    playBackgroundMusic() {
        if (this.backgroundMusic) {
            this.backgroundMusic.play().catch(e => console.log('Autoplay prevented'));
        }
    }

    stopBackgroundMusic() {
        if (this.backgroundMusic) {
            this.backgroundMusic.pause();
        }
    }

    playCorrectSound() {
        if (this.correctSound) {
            this.correctSound.currentTime = 0;
            this.correctSound.play().catch(e => console.log('Sound play failed'));
        }
    }

    playWrongSound() {
        if (this.wrongSound) {
            this.wrongSound.currentTime = 0;
            this.wrongSound.play().catch(e => console.log('Sound play failed'));
        }
    }
}

const audioManager = new AudioManager();

// Gestionnaire de timer
class TimerManager {
    static startTimer(duration, callback) {
        gameState.timeRemaining = duration;
        this.updateDisplay();
        
        gameState.timer = setInterval(() => {
            gameState.timeRemaining--;
            this.updateDisplay();
            
            if (gameState.timeRemaining <= 0) {
                clearInterval(gameState.timer);
                gameState.timer = null;
                if (callback) callback();
            }
        }, 1000);
    }

    static stopTimer() {
        if (gameState.timer) {
            clearInterval(gameState.timer);
            gameState.timer = null;
        }
    }

    static updateDisplay() {
        const display = document.getElementById('timer-display');
        if (display) {
            const minutes = Math.floor(gameState.timeRemaining / 60);
            const seconds = gameState.timeRemaining % 60;
            display.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            
            // Changer la couleur si le temps est critique
            if (gameState.timeRemaining <= 30) {
                display.style.color = 'var(--error-color)';
            } else if (gameState.timeRemaining <= 60) {
                display.style.color = 'var(--warning-color)';
            } else {
                display.style.color = 'var(--text-light)';
            }
        }
    }
}

// Initialisation des événements
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
    applySettings();
    updateRankingsDisplay();
    audioManager.playBackgroundMusic();
});

function initializeEventListeners() {
    // Écran d'accueil
    const difficultyButtons = document.querySelectorAll('.difficulty-btn');
    difficultyButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            // Désélectionner tous les boutons
            difficultyButtons.forEach(b => b.classList.remove('selected'));
            // Sélectionner le bouton cliqué
            this.classList.add('selected');
            gameState.selectedDifficulty = this.dataset.difficulty;
            
            // Activer le bouton de démarrage
            const startBtn = document.getElementById('start-game-btn');
            startBtn.disabled = false;
        });
    });

    // Bouton de démarrage
    document.getElementById('start-game-btn').addEventListener('click', function() {
        if (gameState.selectedDifficulty) {
            ScreenManager.showScreen('main-menu');
        }
    });

    // Boutons du menu principal
    document.getElementById('solo-mode-btn').addEventListener('click', function() {
        // Afficher la sélection de thème ou démarrer directement
        showThemeSelection();
    });

    document.getElementById('multiplayer-mode-btn').addEventListener('click', function() {
        alert('Mode multijoueur - Fonctionnalité à venir !');
    });

    document.getElementById('daily-challenge-btn').addEventListener('click', function() {
        startDailyChallenge();
    });

    // Boutons de thème
    const themeButtons = document.querySelectorAll('.theme-btn');
    themeButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            gameState.currentTheme = this.dataset.theme;
            startGame();
        });
    });

    // Boutons de navigation
    document.getElementById('settings-btn').addEventListener('click', function() {
        ScreenManager.showScreen('settings-screen');
    });

    document.getElementById('rankings-btn').addEventListener('click', function() {
        ScreenManager.showScreen('rankings-screen');
        updateRankingsDisplay();
    });

    document.getElementById('credits-btn').addEventListener('click', function() {
        ScreenManager.showScreen('credits-screen');
    });

    // Boutons de retour
    document.getElementById('settings-back-btn').addEventListener('click', function() {
        ScreenManager.showScreen('main-menu');
    });

    document.getElementById('rankings-back-btn').addEventListener('click', function() {
        ScreenManager.showScreen('main-menu');
    });

    document.getElementById('credits-back-btn').addEventListener('click', function() {
        ScreenManager.showScreen('main-menu');
    });

    // Paramètres
    initializeSettingsListeners();

    // Classements
    initializeRankingsListeners();

    // Contrôles de jeu
    document.getElementById('quit-game-btn').addEventListener('click', function() {
        quitGame();
    });

    document.getElementById('hint-btn').addEventListener('click', function() {
        showHint();
    });

    document.getElementById('skip-btn').addEventListener('click', function() {
        skipQuestion();
    });

    // Modales
    document.getElementById('continue-btn').addEventListener('click', function() {
        ScreenManager.hideModal();
        nextQuestion();
    });

    document.getElementById('menu-btn').addEventListener('click', function() {
        ScreenManager.hideModal();
        ScreenManager.showScreen('main-menu');
        gameState.resetGame();
    });
}

function initializeSettingsListeners() {
    // Volume de la musique
    const musicVolumeSlider = document.getElementById('music-volume');
    musicVolumeSlider.addEventListener('input', function() {
        gameState.settings.musicVolume = parseInt(this.value);
        document.querySelector('#music-volume + .volume-value').textContent = this.value + '%';
        audioManager.updateVolumes();
        gameState.saveSettings();
    });

    // Volume des effets
    const sfxVolumeSlider = document.getElementById('sfx-volume');
    sfxVolumeSlider.addEventListener('input', function() {
        gameState.settings.sfxVolume = parseInt(this.value);
        document.querySelector('#sfx-volume + .volume-value').textContent = this.value + '%';
        audioManager.updateVolumes();
        gameState.saveSettings();
    });

    // Langue
    document.getElementById('language-select').addEventListener('change', function() {
        gameState.settings.language = this.value;
        gameState.saveSettings();
        // Ici on pourrait implémenter la traduction
    });

    // Difficulté par défaut
    document.getElementById('default-difficulty').addEventListener('change', function() {
        gameState.settings.defaultDifficulty = this.value;
        gameState.saveSettings();
    });

    // Mode daltonien
    document.getElementById('colorblind-mode').addEventListener('change', function() {
        gameState.settings.colorblindMode = this.checked;
        document.body.classList.toggle('colorblind-mode', this.checked);
        gameState.saveSettings();
    });

    // Contraste élevé
    document.getElementById('high-contrast').addEventListener('change', function() {
        gameState.settings.highContrast = this.checked;
        document.body.classList.toggle('high-contrast', this.checked);
        gameState.saveSettings();
    });
}

function initializeRankingsListeners() {
    const tabButtons = document.querySelectorAll('.tab-btn');
    tabButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            // Désactiver tous les onglets
            tabButtons.forEach(b => b.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
            
            // Activer l'onglet sélectionné
            this.classList.add('active');
            const tabId = this.dataset.tab + '-rankings';
            document.getElementById(tabId).classList.add('active');
        });
    });
}

function applySettings() {
    // Appliquer les paramètres sauvegardés
    document.getElementById('music-volume').value = gameState.settings.musicVolume;
    document.getElementById('sfx-volume').value = gameState.settings.sfxVolume;
    document.getElementById('language-select').value = gameState.settings.language;
    document.getElementById('default-difficulty').value = gameState.settings.defaultDifficulty;
    document.getElementById('colorblind-mode').checked = gameState.settings.colorblindMode;
    document.getElementById('high-contrast').checked = gameState.settings.highContrast;

    // Appliquer les classes CSS
    document.body.classList.toggle('colorblind-mode', gameState.settings.colorblindMode);
    document.body.classList.toggle('high-contrast', gameState.settings.highContrast);

    // Mettre à jour les affichages de volume
    document.querySelector('#music-volume + .volume-value').textContent = gameState.settings.musicVolume + '%';
    document.querySelector('#sfx-volume + .volume-value').textContent = gameState.settings.sfxVolume + '%';

    audioManager.updateVolumes();
}

// Base de données des questions
const questionsDatabase = {
    mathematiques: {
        primaire: {
            facile: [
                {
                    question: "Combien font 5 + 3 ?",
                    answers: ["6", "7", "8", "9"],
                    correct: 2,
                    type: "multiple"
                },
                {
                    question: "Quelle est la forme géométrique qui a 3 côtés ?",
                    answers: ["Carré", "Triangle", "Cercle", "Rectangle"],
                    correct: 1,
                    type: "multiple"
                },
                {
                    question: "Combien font 10 - 4 ?",
                    answers: ["5", "6", "7", "8"],
                    correct: 1,
                    type: "multiple"
                },
                {
                    question: "Combien font 9 × 6 ?",
                    answers: ["52", "54", "56", "58"],
                    correct: 1,
                    type: "multiple"
                },
                {
                    question: "Quelle est la moitié de 20 ?",
                    answers: ["8", "10", "12", "15"],
                    correct: 1,
                    type: "multiple"
                },
                {
                    question: "Combien de côtés a un carré ?",
                    answers: ["3", "4", "5", "6"],
                    correct: 1,
                    type: "multiple"
                },
                {
                    question: "Combien font 15 + 7 ?",
                    answers: ["20", "21", "22", "23"],
                    correct: 2,
                    type: "multiple"
                },
                {
                    question: "Quelle est la table de multiplication de 5 × 4 ?",
                    answers: ["15", "20", "25", "30"],
                    correct: 1,
                    type: "multiple"
                }
            ],
            intermediaire: [
                {
                    question: "Combien font 12 × 7 ?",
                    answers: ["82", "84", "86", "88"],
                    correct: 1,
                    type: "multiple"
                },
                {
                    question: "Quelle est la racine carrée de 64 ?",
                    answers: ["6", "7", "8", "9"],
                    correct: 2,
                    type: "multiple"
                },
                {
                    question: "Combien font 144 ÷ 12 ?",
                    answers: ["10", "11", "12", "13"],
                    correct: 2,
                    type: "multiple"
                },
                {
                    question: "Quelle est l'aire d'un carré de côté 6 cm ?",
                    answers: ["24 cm²", "30 cm²", "36 cm²", "42 cm²"],
                    correct: 2,
                    type: "multiple"
                }
            ],
            difficile: [
                {
                    question: "Résolvez : 3x + 5 = 14. Que vaut x ?",
                    answers: ["2", "3", "4", "5"],
                    correct: 1,
                    type: "multiple"
                },
                {
                    question: "Quelle est la valeur de π (pi) arrondie à 2 décimales ?",
                    answers: ["3.12", "3.14", "3.16", "3.18"],
                    correct: 1,
                    type: "multiple"
                }
            ]
        },
        college: {
            facile: [
                {
                    question: "Quelle est la formule de l'aire d'un rectangle ?",
                    answers: ["L × l", "2(L + l)", "L + l", "L²"],
                    correct: 0,
                    type: "multiple"
                },
                {
                    question: "Combien font (-5) + 8 ?",
                    answers: ["3", "-3", "13", "-13"],
                    correct: 0,
                    type: "multiple"
                }
            ]
        }
    },
    histoire: {
        primaire: {
            facile: [
                {
                    question: "En quelle année a eu lieu la Révolution française ?",
                    answers: ["1789", "1799", "1804", "1815"],
                    correct: 0,
                    type: "multiple"
                },
                {
                    question: "Qui était le roi de France pendant la Révolution ?",
                    answers: ["Louis XIV", "Louis XV", "Louis XVI", "Napoléon"],
                    correct: 2,
                    type: "multiple"
                },
                {
                    question: "En quelle année Christophe Colomb a-t-il découvert l'Amérique ?",
                    answers: ["1490", "1492", "1494", "1496"],
                    correct: 1,
                    type: "multiple"
                },
                {
                    question: "Qui était Jules César ?",
                    answers: ["Un roi français", "Un empereur romain", "Un pharaon", "Un général romain"],
                    correct: 3,
                    type: "multiple"
                },
                {
                    question: "Quelle guerre a eu lieu de 1914 à 1918 ?",
                    answers: ["Guerre de Cent Ans", "Première Guerre mondiale", "Seconde Guerre mondiale", "Guerre de Troie"],
                    correct: 1,
                    type: "multiple"
                }
            ],
            intermediaire: [
                {
                    question: "Qui a écrit 'Les Misérables' ?",
                    answers: ["Émile Zola", "Victor Hugo", "Alexandre Dumas", "Gustave Flaubert"],
                    correct: 1,
                    type: "multiple"
                }
            ]
        }
    },
    geographie: {
        primaire: {
            facile: [
                {
                    question: "Quelle est la capitale de la France ?",
                    answers: ["Lyon", "Marseille", "Paris", "Toulouse"],
                    correct: 2,
                    type: "multiple"
                },
                {
                    question: "Quel est le plus grand océan du monde ?",
                    answers: ["Atlantique", "Indien", "Arctique", "Pacifique"],
                    correct: 3,
                    type: "multiple"
                },
                {
                    question: "Quelle est la capitale de l'Italie ?",
                    answers: ["Milan", "Naples", "Rome", "Venise"],
                    correct: 2,
                    type: "multiple"
                },
                {
                    question: "Sur quel continent se trouve l'Égypte ?",
                    answers: ["Asie", "Afrique", "Europe", "Amérique"],
                    correct: 1,
                    type: "multiple"
                },
                {
                    question: "Quel est le plus long fleuve du monde ?",
                    answers: ["Amazone", "Nil", "Mississippi", "Yangtsé"],
                    correct: 1,
                    type: "multiple"
                },
                {
                    question: "Combien y a-t-il de continents ?",
                    answers: ["5", "6", "7", "8"],
                    correct: 2,
                    type: "multiple"
                }
            ]
        }
    },
    physique: {
        primaire: {
            facile: [
                {
                    question: "Combien y a-t-il de couleurs dans l'arc-en-ciel ?",
                    answers: ["5", "6", "7", "8"],
                    correct: 2,
                    type: "multiple"
                },
                {
                    question: "Que se passe-t-il quand on mélange du rouge et du bleu ?",
                    answers: ["Vert", "Violet", "Orange", "Jaune"],
                    correct: 1,
                    type: "multiple"
                },
                {
                    question: "À quelle température l'eau bout-elle ?",
                    answers: ["90°C", "95°C", "100°C", "105°C"],
                    correct: 2,
                    type: "multiple"
                },
                {
                    question: "Combien de phases a la Lune ?",
                    answers: ["2", "4", "6", "8"],
                    correct: 3,
                    type: "multiple"
                }
            ]
        }
    },
    arts: {
        primaire: {
            facile: [
                {
                    question: "Qui a peint la Joconde ?",
                    answers: ["Picasso", "Van Gogh", "Léonard de Vinci", "Monet"],
                    correct: 2,
                    type: "multiple"
                },
                {
                    question: "Dans quel musée se trouve la Joconde ?",
                    answers: ["Musée d'Orsay", "Louvre", "Centre Pompidou", "Musée Rodin"],
                    correct: 1,
                    type: "multiple"
                },
                {
                    question: "Qui a composé 'La Marseillaise' ?",
                    answers: ["Mozart", "Beethoven", "Rouget de Lisle", "Chopin"],
                    correct: 2,
                    type: "multiple"
                },
                {
                    question: "Quelle couleur obtient-on en mélangeant jaune et rouge ?",
                    answers: ["Vert", "Violet", "Orange", "Rose"],
                    correct: 2,
                    type: "multiple"
                }
            ]
        }
    },
    personnalites: {
        primaire: {
            facile: [
                {
                    question: "Qui a écrit 'Le Petit Prince' ?",
                    answers: ["Jules Verne", "Antoine de Saint-Exupéry", "Victor Hugo", "Molière"],
                    correct: 1,
                    type: "multiple"
                },
                {
                    question: "Qui était Napoléon Bonaparte ?",
                    answers: ["Un roi", "Un empereur", "Un président", "Un ministre"],
                    correct: 1,
                    type: "multiple"
                },
                {
                    question: "Qui a inventé l'ampoule électrique ?",
                    answers: ["Alexander Graham Bell", "Thomas Edison", "Nikola Tesla", "Benjamin Franklin"],
                    correct: 1,
                    type: "multiple"
                },
                {
                    question: "Qui était Marie Curie ?",
                    answers: ["Une artiste", "Une scientifique", "Une écrivaine", "Une musicienne"],
                    correct: 1,
                    type: "multiple"
                }
            ]
        }
    }
};

// Fonctions de jeu
function showThemeSelection() {
    // Pour l'instant, on reste sur le menu principal
    // Les boutons de thème sont déjà visibles
}

function startGame() {
    if (!gameState.currentTheme) {
        alert('Veuillez sélectionner un thème');
        return;
    }

    gameState.resetGame();
    loadQuestions();
    ScreenManager.showScreen('game-screen');
    updateGameDisplay();
    displayCurrentQuestion();
    TimerManager.startTimer(120, onTimeUp); // 2 minutes
}

function startDailyChallenge() {
    // Vérifier si le défi quotidien a déjà été fait aujourd'hui
    const today = new Date().toDateString();
    const lastDaily = localStorage.getItem('lastDailyChallenge');

    if (lastDaily === today) {
        alert('Vous avez déjà fait le défi quotidien aujourd\'hui ! Revenez demain pour un nouveau défi.');
        return;
    }

    // Générer le défi quotidien basé sur la date
    const dailyChallenge = generateDailyChallenge();
    gameState.currentTheme = dailyChallenge.theme;
    gameState.selectedDifficulty = dailyChallenge.difficulty;
    gameState.currentLevel = dailyChallenge.level;

    // Marquer le défi comme fait
    localStorage.setItem('lastDailyChallenge', today);

    alert(`Défi quotidien : ${getThemeName(dailyChallenge.theme)} - ${getLevelName(dailyChallenge.level)} (${getDifficultyName(dailyChallenge.difficulty)}) !`);
    startGame();
}

function generateDailyChallenge() {
    // Utiliser la date comme seed pour générer un défi reproductible
    const today = new Date();
    const seed = today.getFullYear() * 10000 + (today.getMonth() + 1) * 100 + today.getDate();

    // Générateur pseudo-aléatoire basé sur la date
    const random = (seed) => {
        const x = Math.sin(seed) * 10000;
        return x - Math.floor(x);
    };

    const themes = Object.keys(questionsDatabase);
    const levels = ['primaire', 'college', 'lycee'];
    const difficulties = ['facile', 'intermediaire', 'difficile'];

    const themeIndex = Math.floor(random(seed) * themes.length);
    const levelIndex = Math.floor(random(seed + 1) * levels.length);
    const difficultyIndex = Math.floor(random(seed + 2) * difficulties.length);

    return {
        theme: themes[themeIndex],
        level: levels[levelIndex],
        difficulty: difficulties[difficultyIndex]
    };
}

// Système de reconnaissance visuelle
function addVisualRecognitionQuestions() {
    // Ajouter des questions avec images pour la reconnaissance visuelle
    const visualQuestions = {
        personnalites: {
            primaire: {
                facile: [
                    {
                        question: "Qui est cette personnalité historique ?",
                        image: "images/napoleon.jpg",
                        answers: ["Louis XIV", "Napoléon Bonaparte", "Charles de Gaulle", "François Mitterrand"],
                        correct: 1,
                        type: "multiple"
                    },
                    {
                        question: "Reconnaissez-vous cet écrivain ?",
                        image: "images/victor_hugo.jpg",
                        answers: ["Victor Hugo", "Émile Zola", "Alexandre Dumas", "Gustave Flaubert"],
                        correct: 0,
                        type: "multiple"
                    }
                ]
            }
        },
        geographie: {
            primaire: {
                facile: [
                    {
                        question: "Quel monument est-ce ?",
                        image: "images/tour_eiffel.jpg",
                        answers: ["Arc de Triomphe", "Tour Eiffel", "Notre-Dame", "Sacré-Cœur"],
                        correct: 1,
                        type: "multiple"
                    },
                    {
                        question: "Dans quel pays se trouve ce monument ?",
                        image: "images/colisee.jpg",
                        answers: ["France", "Espagne", "Italie", "Grèce"],
                        correct: 2,
                        type: "multiple"
                    }
                ]
            }
        },
        arts: {
            primaire: {
                facile: [
                    {
                        question: "Quel est le nom de cette œuvre ?",
                        image: "images/joconde.jpg",
                        answers: ["La Vénus de Milo", "La Joconde", "La Liberté guidant le peuple", "Les Tournesols"],
                        correct: 1,
                        type: "multiple"
                    }
                ]
            }
        }
    };

    // Fusionner avec la base de données existante
    Object.keys(visualQuestions).forEach(theme => {
        if (questionsDatabase[theme]) {
            Object.keys(visualQuestions[theme]).forEach(level => {
                if (questionsDatabase[theme][level]) {
                    Object.keys(visualQuestions[theme][level]).forEach(difficulty => {
                        if (questionsDatabase[theme][level][difficulty]) {
                            questionsDatabase[theme][level][difficulty].push(...visualQuestions[theme][level][difficulty]);
                        }
                    });
                }
            });
        }
    });
}

// Mode RANKED
class RankedMode {
    static currentSeason = 1;
    static playerRank = {
        points: 1000,
        tier: 'Bronze',
        division: 3
    };

    static tiers = [
        { name: 'Bronze', divisions: 3, minPoints: 0 },
        { name: 'Argent', divisions: 3, minPoints: 1200 },
        { name: 'Or', divisions: 3, minPoints: 1500 },
        { name: 'Platine', divisions: 3, minPoints: 1800 },
        { name: 'Diamant', divisions: 3, minPoints: 2100 },
        { name: 'Maître', divisions: 1, minPoints: 2400 },
        { name: 'Grand Maître', divisions: 1, minPoints: 2700 }
    ];

    static calculateRankPoints(performance) {
        const basePoints = 25;
        const performanceMultiplier = performance.percentage / 100;
        const speedBonus = Math.max(0, (120 - performance.timeSpent) / 10);

        return Math.round(basePoints * performanceMultiplier + speedBonus);
    }

    static updateRank(points) {
        this.playerRank.points += points;

        // Calculer le nouveau tier et division
        for (let i = this.tiers.length - 1; i >= 0; i--) {
            const tier = this.tiers[i];
            if (this.playerRank.points >= tier.minPoints) {
                this.playerRank.tier = tier.name;

                if (tier.divisions > 1) {
                    const pointsInTier = this.playerRank.points - tier.minPoints;
                    const nextTier = this.tiers[i + 1];
                    const tierRange = nextTier ? nextTier.minPoints - tier.minPoints : 300;
                    const divisionSize = tierRange / tier.divisions;
                    this.playerRank.division = Math.min(tier.divisions, Math.floor(pointsInTier / divisionSize) + 1);
                } else {
                    this.playerRank.division = 1;
                }
                break;
            }
        }

        // Sauvegarder
        localStorage.setItem('primevalQuizRank', JSON.stringify(this.playerRank));
    }

    static loadRank() {
        const saved = localStorage.getItem('primevalQuizRank');
        if (saved) {
            this.playerRank = JSON.parse(saved);
        }
    }

    static getRankDisplay() {
        if (this.playerRank.tier === 'Maître' || this.playerRank.tier === 'Grand Maître') {
            return this.playerRank.tier;
        }
        return `${this.playerRank.tier} ${this.playerRank.division}`;
    }
}

// Système d'indices
function showHint() {
    const question = gameState.questions[gameState.currentQuestionIndex];
    if (!question) return;

    // Générer un indice basé sur la question
    let hint = generateHint(question);

    const hintModal = document.createElement('div');
    hintModal.className = 'modal-overlay';
    hintModal.innerHTML = `
        <div class="modal">
            <div class="modal-content">
                <h3><i class="fas fa-lightbulb"></i> Indice</h3>
                <p>${hint}</p>
                <div class="modal-buttons">
                    <button class="modal-btn" onclick="this.closest('.modal-overlay').remove()">Compris !</button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(hintModal);
    hintModal.classList.add('active');
}

function generateHint(question) {
    const hints = {
        "Quelle est la capitale de la France ?": "C'est la ville où se trouve la Tour Eiffel.",
        "Combien font 5 + 3 ?": "Comptez sur vos doigts si nécessaire !",
        "Qui a peint la Joconde ?": "Cet artiste était aussi inventeur et scientifique.",
        "En quelle année a eu lieu la Révolution française ?": "C'est à la fin du 18ème siècle.",
        "Quel est le plus grand océan du monde ?": "Il se trouve entre l'Asie et l'Amérique."
    };

    return hints[question.question] || "Réfléchissez bien à la question et éliminez les réponses qui vous semblent incorrectes.";
}

// Système de sauvegarde automatique
function autoSave() {
    try {
        gameState.saveSettings();
        gameState.saveRankings();
        console.log('Sauvegarde automatique effectuée');
    } catch (error) {
        console.error('Erreur lors de la sauvegarde:', error);
    }
}

// Sauvegarde automatique toutes les 30 secondes
setInterval(autoSave, 30000);

// Gestion des erreurs globales
window.addEventListener('error', function(event) {
    console.error('Erreur JavaScript:', event.error);
    // Afficher un message d'erreur convivial à l'utilisateur
    showErrorMessage('Une erreur inattendue s\'est produite. Le jeu va continuer normalement.');
});

function showErrorMessage(message) {
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-notification';
    errorDiv.innerHTML = `
        <div class="error-content">
            <i class="fas fa-exclamation-triangle"></i>
            <span>${message}</span>
        </div>
    `;
    errorDiv.style.cssText = `
        position: fixed;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        background: var(--error-color);
        color: white;
        padding: 1rem 2rem;
        border-radius: var(--border-radius);
        z-index: 1004;
        animation: slideInDown 0.5s ease-out;
        box-shadow: var(--shadow);
    `;

    document.body.appendChild(errorDiv);

    setTimeout(() => {
        errorDiv.style.animation = 'slideOutUp 0.5s ease-in';
        setTimeout(() => errorDiv.remove(), 500);
    }, 5000);
}

// Amélioration de la fonction de fin de jeu
function endGame() {
    TimerManager.stopTimer();

    const totalQuestions = gameState.questions.length;
    const correctAnswers = gameState.userAnswers.filter(a => a && a.correct).length;
    const percentage = totalQuestions > 0 ? Math.round((correctAnswers / totalQuestions) * 100) : 0;

    // Calculer des statistiques détaillées
    const averageTime = gameState.userAnswers.length > 0
        ? gameState.userAnswers.reduce((sum, answer) => sum + (answer ? answer.timeSpent : 0), 0) / gameState.userAnswers.length
        : 0;

    const gameStats = {
        correct: correctAnswers,
        total: totalQuestions,
        percentage: percentage,
        averageTime: Math.round(averageTime),
        theme: gameState.currentTheme,
        level: gameState.currentLevel,
        difficulty: gameState.selectedDifficulty
    };

    // Sauvegarder le score
    saveScore(correctAnswers, totalQuestions, percentage);

    // Afficher les résultats détaillés
    showDetailedResults(gameStats);
}

function showDetailedResults(stats) {
    const modal = document.getElementById('result-modal');
    const title = document.getElementById('result-title');
    const message = document.getElementById('result-message');

    // Déterminer le titre et la couleur basés sur la performance
    if (stats.percentage >= 90) {
        title.textContent = 'Exceptionnel !';
        title.style.color = 'var(--success-color)';
    } else if (stats.percentage >= 80) {
        title.textContent = 'Excellent !';
        title.style.color = 'var(--success-color)';
    } else if (stats.percentage >= 70) {
        title.textContent = 'Très bien !';
        title.style.color = 'var(--primary-color)';
    } else if (stats.percentage >= 60) {
        title.textContent = 'Bien joué !';
        title.style.color = 'var(--warning-color)';
    } else {
        title.textContent = 'Peut mieux faire';
        title.style.color = 'var(--error-color)';
    }

    // Message détaillé
    message.innerHTML = `
        <div class="result-stats">
            <div class="stat-row">
                <span class="stat-label">Score :</span>
                <span class="stat-value">${stats.correct}/${stats.total} (${stats.percentage}%)</span>
            </div>
            <div class="stat-row">
                <span class="stat-label">Temps moyen :</span>
                <span class="stat-value">${stats.averageTime}s par question</span>
            </div>
            <div class="stat-row">
                <span class="stat-label">Thème :</span>
                <span class="stat-value">${getThemeName(stats.theme)}</span>
            </div>
            <div class="stat-row">
                <span class="stat-label">Niveau :</span>
                <span class="stat-value">${getLevelName(stats.level)} - ${getDifficultyName(stats.difficulty)}</span>
            </div>
        </div>
    `;

    ScreenManager.showModal('result-modal');
}

// Fonction pour précharger les ressources
function preloadResources() {
    // Précharger les sons
    const sounds = ['correct-sound', 'wrong-sound'];
    sounds.forEach(soundId => {
        const audio = document.getElementById(soundId);
        if (audio) {
            audio.load();
        }
    });

    // Précharger les images communes
    const commonImages = [
        'images/tour_eiffel.jpg',
        'images/joconde.jpg',
        'images/napoleon.jpg'
    ];

    commonImages.forEach(src => {
        const img = new Image();
        img.src = src;
    });
}

// Fonction d'optimisation des performances
function optimizePerformance() {
    // Nettoyer les anciens scores (garder seulement les 50 derniers)
    if (gameState.rankings.solo.progress.length > 50) {
        gameState.rankings.solo.progress = gameState.rankings.solo.progress
            .sort((a, b) => new Date(b.date) - new Date(a.date))
            .slice(0, 50);
        gameState.saveRankings();
    }

    // Nettoyer le localStorage si nécessaire
    try {
        const usage = JSON.stringify(localStorage).length;
        if (usage > 5000000) { // 5MB
            console.log('Nettoyage du localStorage nécessaire');
            // Garder seulement les données essentielles
            const essential = {
                primevalQuizSettings: localStorage.getItem('primevalQuizSettings'),
                primevalQuizCurrentLevel: localStorage.getItem('primevalQuizCurrentLevel'),
                primevalQuizRankings: localStorage.getItem('primevalQuizRankings')
            };

            localStorage.clear();

            Object.keys(essential).forEach(key => {
                if (essential[key]) {
                    localStorage.setItem(key, essential[key]);
                }
            });
        }
    } catch (error) {
        console.error('Erreur lors de l\'optimisation:', error);
    }
}

// Initialiser les fonctionnalités spéciales
document.addEventListener('DOMContentLoaded', function() {
    try {
        addVisualRecognitionQuestions();
        RankedMode.loadRank();
        preloadResources();

        // Optimiser les performances après un délai
        setTimeout(optimizePerformance, 5000);

        console.log('Primeval Quiz initialisé avec succès !');
    } catch (error) {
        console.error('Erreur lors de l\'initialisation:', error);
        showErrorMessage('Erreur lors du chargement du jeu. Certaines fonctionnalités peuvent être limitées.');
    }
});

function loadQuestions() {
    const themeData = questionsDatabase[gameState.currentTheme];
    if (!themeData) return;

    const levelData = themeData[gameState.currentLevel];
    if (!levelData) return;

    const difficultyData = levelData[gameState.selectedDifficulty];
    if (!difficultyData) return;

    // Mélanger les questions
    gameState.questions = [...difficultyData].sort(() => Math.random() - 0.5);

    // Limiter à 10 questions maximum
    if (gameState.questions.length > 10) {
        gameState.questions = gameState.questions.slice(0, 10);
    }
}

function updateGameDisplay() {
    document.getElementById('current-theme').textContent = getThemeName(gameState.currentTheme);
    document.getElementById('current-game-level').textContent = `${getLevelName(gameState.currentLevel)} - ${getDifficultyName(gameState.selectedDifficulty)}`;
    document.getElementById('score-display').textContent = gameState.score;
    document.getElementById('errors-display').textContent = `${gameState.errors}/3`;
    document.getElementById('total-questions').textContent = gameState.questions.length;
}

function displayCurrentQuestion() {
    if (gameState.currentQuestionIndex >= gameState.questions.length) {
        endGame();
        return;
    }

    const question = gameState.questions[gameState.currentQuestionIndex];
    document.getElementById('question-num').textContent = gameState.currentQuestionIndex + 1;
    document.getElementById('question-text').textContent = question.question;

    // Afficher l'image si elle existe
    const questionImage = document.getElementById('question-image');
    const questionImg = document.getElementById('question-img');

    if (question.image) {
        questionImg.src = question.image;
        questionImg.alt = "Image de la question";
        questionImage.style.display = 'block';

        // Gérer les erreurs de chargement d'image
        questionImg.onerror = function() {
            questionImage.style.display = 'none';
            console.log('Image non trouvée:', question.image);
        };
    } else {
        questionImage.style.display = 'none';
    }

    // Afficher les réponses
    displayAnswers(question);

    // Ajouter une animation d'entrée
    const questionContainer = document.querySelector('.question-container');
    questionContainer.style.animation = 'slideInUp 0.5s ease-out';
}

function displayAnswers(question) {
    const container = document.getElementById('answers-container');
    container.innerHTML = '';

    if (question.type === 'multiple') {
        question.answers.forEach((answer, index) => {
            const button = document.createElement('button');
            button.className = 'answer-btn';
            button.textContent = answer;
            button.addEventListener('click', () => selectAnswer(index));
            container.appendChild(button);
        });
    } else if (question.type === 'input') {
        const input = document.createElement('input');
        input.type = 'text';
        input.className = 'answer-input';
        input.placeholder = 'Tapez votre réponse...';
        input.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                selectAnswer(input.value);
            }
        });
        container.appendChild(input);

        const submitBtn = document.createElement('button');
        submitBtn.className = 'answer-btn';
        submitBtn.textContent = 'Valider';
        submitBtn.addEventListener('click', () => selectAnswer(input.value));
        container.appendChild(submitBtn);
    }
}

function selectAnswer(answerIndex) {
    const question = gameState.questions[gameState.currentQuestionIndex];
    const isCorrect = answerIndex === question.correct;

    // Marquer la réponse
    gameState.userAnswers[gameState.currentQuestionIndex] = {
        answer: answerIndex,
        correct: isCorrect,
        timeSpent: 120 - gameState.timeRemaining
    };

    // Mettre à jour le score et les erreurs
    if (isCorrect) {
        gameState.score += 10;
        audioManager.playCorrectSound();
        showFeedback('Correct !', 'success');
    } else {
        gameState.errors++;
        audioManager.playWrongSound();
        showFeedback('Incorrect !', 'error');

        if (gameState.errors >= 3) {
            endGame();
            return;
        }
    }

    // Mettre à jour l'affichage
    updateGameDisplay();

    // Marquer visuellement les réponses
    markAnswers(question, answerIndex);

    // Passer à la question suivante après un délai
    setTimeout(() => {
        nextQuestion();
    }, 1500);
}

function markAnswers(question, selectedIndex) {
    const answerButtons = document.querySelectorAll('.answer-btn');
    answerButtons.forEach((btn, index) => {
        if (index === question.correct) {
            btn.classList.add('correct');
        } else if (index === selectedIndex && selectedIndex !== question.correct) {
            btn.classList.add('incorrect');
        }
        btn.disabled = true;
    });
}

function showFeedback(message, type) {
    // Créer un élément de feedback temporaire
    const feedback = document.createElement('div');
    feedback.className = `feedback ${type}`;
    feedback.textContent = message;
    feedback.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: ${type === 'success' ? 'var(--success-color)' : 'var(--error-color)'};
        color: white;
        padding: 1rem 2rem;
        border-radius: var(--border-radius);
        font-weight: 600;
        z-index: 1001;
        animation: fadeIn 0.3s ease-in-out;
    `;

    document.body.appendChild(feedback);

    setTimeout(() => {
        feedback.remove();
    }, 1500);
}

function nextQuestion() {
    gameState.currentQuestionIndex++;
    displayCurrentQuestion();
}

function endGame() {
    TimerManager.stopTimer();

    const totalQuestions = gameState.questions.length;
    const correctAnswers = gameState.userAnswers.filter(a => a.correct).length;
    const percentage = Math.round((correctAnswers / totalQuestions) * 100);

    // Sauvegarder le score
    saveScore(correctAnswers, totalQuestions, percentage);

    // Afficher les résultats
    showResults(correctAnswers, totalQuestions, percentage);
}

function saveScore(correct, total, percentage) {
    const scoreData = {
        theme: gameState.currentTheme,
        level: gameState.currentLevel,
        difficulty: gameState.selectedDifficulty,
        correct: correct,
        total: total,
        percentage: percentage,
        date: new Date().toISOString(),
        timeSpent: 120 - gameState.timeRemaining
    };

    // Ajouter aux classements
    gameState.rankings.solo.progress.push(scoreData);

    // Garder seulement les 100 meilleurs scores
    gameState.rankings.solo.progress.sort((a, b) => b.percentage - a.percentage);
    if (gameState.rankings.solo.progress.length > 100) {
        gameState.rankings.solo.progress = gameState.rankings.solo.progress.slice(0, 100);
    }

    // Vérifier la progression de niveau
    checkLevelProgression(scoreData);

    // Vérifier les achievements
    AchievementSystem.checkAchievements({
        ...scoreData,
        gamesPlayed: gameState.rankings.solo.progress.length
    });

    gameState.saveRankings();
}

// Système de progression par niveaux
function checkLevelProgression(scoreData) {
    const currentLevelIndex = getLevelIndex(gameState.currentLevel);
    const requiredPercentage = 70; // 70% requis pour passer au niveau suivant

    if (scoreData.percentage >= requiredPercentage) {
        // Vérifier si le joueur peut passer au niveau suivant
        const levelScores = gameState.rankings.solo.progress.filter(score =>
            score.level === gameState.currentLevel &&
            score.difficulty === gameState.selectedDifficulty
        );

        // Calculer la moyenne des 3 derniers scores
        const recentScores = levelScores.slice(-3);
        const averageScore = recentScores.reduce((sum, score) => sum + score.percentage, 0) / recentScores.length;

        if (recentScores.length >= 3 && averageScore >= requiredPercentage) {
            const nextLevel = getNextLevel(gameState.currentLevel);
            if (nextLevel && nextLevel !== gameState.currentLevel) {
                showLevelUpNotification(nextLevel);
                gameState.currentLevel = nextLevel;
                updateCurrentLevelDisplay();

                // Sauvegarder la progression
                localStorage.setItem('primevalQuizCurrentLevel', gameState.currentLevel);
            }
        }
    }
}

function getLevelIndex(level) {
    const levels = ['primaire', 'college', 'lycee', 'expert'];
    return levels.indexOf(level);
}

function getNextLevel(currentLevel) {
    const levels = ['primaire', 'college', 'lycee', 'expert'];
    const currentIndex = levels.indexOf(currentLevel);
    return currentIndex < levels.length - 1 ? levels[currentIndex + 1] : null;
}

function showLevelUpNotification(newLevel) {
    const notification = document.createElement('div');
    notification.className = 'level-up-notification';
    notification.innerHTML = `
        <div class="level-up-content">
            <i class="fas fa-trophy"></i>
            <div>
                <h3>Niveau supérieur débloqué !</h3>
                <p>Vous passez au niveau <strong>${getLevelName(newLevel)}</strong></p>
                <small>Continuez comme ça !</small>
            </div>
        </div>
    `;
    notification.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: linear-gradient(45deg, var(--success-color), var(--primary-color));
        color: white;
        padding: 2rem;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow);
        z-index: 1003;
        animation: levelUpAnimation 0.8s ease-out;
        text-align: center;
        max-width: 400px;
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.style.animation = 'fadeOut 0.5s ease-in';
        setTimeout(() => notification.remove(), 500);
    }, 4000);
}

function updateCurrentLevelDisplay() {
    const levelDisplay = document.getElementById('current-level');
    if (levelDisplay) {
        levelDisplay.textContent = getLevelName(gameState.currentLevel);
    }
}

// Charger le niveau sauvegardé au démarrage
document.addEventListener('DOMContentLoaded', function() {
    const savedLevel = localStorage.getItem('primevalQuizCurrentLevel');
    if (savedLevel) {
        gameState.currentLevel = savedLevel;
        updateCurrentLevelDisplay();
    }
});

// Système de sélection de niveau et difficulté avancé
function showLevelSelection() {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal level-selection-modal">
            <div class="modal-content">
                <h3>Choisissez votre niveau</h3>
                <div class="level-grid">
                    <button class="level-option ${gameState.currentLevel === 'primaire' ? 'current' : ''}" data-level="primaire">
                        <i class="fas fa-seedling"></i>
                        <span>Primaire</span>
                        <small>Questions de base</small>
                    </button>
                    <button class="level-option ${gameState.currentLevel === 'college' ? 'current' : ''}" data-level="college">
                        <i class="fas fa-graduation-cap"></i>
                        <span>Collège</span>
                        <small>Niveau intermédiaire</small>
                    </button>
                    <button class="level-option ${gameState.currentLevel === 'lycee' ? 'current' : ''}" data-level="lycee">
                        <i class="fas fa-university"></i>
                        <span>Lycée</span>
                        <small>Questions avancées</small>
                    </button>
                    <button class="level-option ${gameState.currentLevel === 'expert' ? 'current' : ''}" data-level="expert">
                        <i class="fas fa-crown"></i>
                        <span>Expert</span>
                        <small>Défi ultime</small>
                    </button>
                </div>
                <div class="modal-buttons">
                    <button class="modal-btn" id="confirm-level">Confirmer</button>
                    <button class="modal-btn secondary" id="cancel-level">Annuler</button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    modal.classList.add('active');

    let selectedLevel = gameState.currentLevel;

    // Gestion des clics sur les niveaux
    modal.querySelectorAll('.level-option').forEach(btn => {
        btn.addEventListener('click', function() {
            modal.querySelectorAll('.level-option').forEach(b => b.classList.remove('selected'));
            this.classList.add('selected');
            selectedLevel = this.dataset.level;
        });
    });

    // Bouton confirmer
    modal.querySelector('#confirm-level').addEventListener('click', function() {
        gameState.currentLevel = selectedLevel;
        updateCurrentLevelDisplay();
        localStorage.setItem('primevalQuizCurrentLevel', gameState.currentLevel);
        modal.remove();
    });

    // Bouton annuler
    modal.querySelector('#cancel-level').addEventListener('click', function() {
        modal.remove();
    });
}

// Ajouter un bouton pour changer de niveau dans le menu
document.addEventListener('DOMContentLoaded', function() {
    const levelIndicator = document.querySelector('.level-indicator');
    if (levelIndicator) {
        levelIndicator.style.cursor = 'pointer';
        levelIndicator.title = 'Cliquez pour changer de niveau';
        levelIndicator.addEventListener('click', showLevelSelection);
    }
});

function showResults(correct, total, percentage) {
    const modal = document.getElementById('result-modal');
    const title = document.getElementById('result-title');
    const message = document.getElementById('result-message');

    if (percentage >= 80) {
        title.textContent = 'Excellent !';
        title.style.color = 'var(--success-color)';
    } else if (percentage >= 60) {
        title.textContent = 'Bien joué !';
        title.style.color = 'var(--warning-color)';
    } else {
        title.textContent = 'Peut mieux faire';
        title.style.color = 'var(--error-color)';
    }

    message.textContent = `Vous avez obtenu ${correct}/${total} bonnes réponses (${percentage}%)`;

    ScreenManager.showModal('result-modal');
}

function quitGame() {
    if (confirm('Êtes-vous sûr de vouloir quitter la partie ?')) {
        TimerManager.stopTimer();
        gameState.resetGame();
        ScreenManager.showScreen('main-menu');
    }
}

function showHint() {
    alert('Fonctionnalité d\'indice à venir !');
}

function skipQuestion() {
    if (confirm('Êtes-vous sûr de vouloir passer cette question ?')) {
        gameState.errors++;
        updateGameDisplay();

        if (gameState.errors >= 3) {
            endGame();
        } else {
            nextQuestion();
        }
    }
}

function onTimeUp() {
    alert('Temps écoulé !');
    endGame();
}

// Fonctions utilitaires
function getThemeName(theme) {
    const names = {
        mathematiques: 'Mathématiques',
        histoire: 'Histoire',
        geographie: 'Géographie',
        physique: 'Physique',
        arts: 'Arts & Culture',
        personnalites: 'Personnalités'
    };
    return names[theme] || theme;
}

function getLevelName(level) {
    const names = {
        primaire: 'Primaire',
        college: 'Collège',
        lycee: 'Lycée',
        expert: 'Expert'
    };
    return names[level] || level;
}

function getDifficultyName(difficulty) {
    const names = {
        facile: 'Facile',
        intermediaire: 'Intermédiaire',
        difficile: 'Difficile'
    };
    return names[difficulty] || difficulty;
}

// Gestion des classements
function updateRankingsDisplay() {
    updateSoloRankings();
    updateMultiplayerRankings();
    updateGlobalRankings();
    updateSidebarRankings();
}

function updateSoloRankings() {
    const container = document.getElementById('solo-rankings');
    if (!container) return;

    const rankings = gameState.rankings.solo.progress;

    container.innerHTML = `
        <div class="ranking-category">
            <h4>Meilleurs scores</h4>
            <div class="ranking-list">
                ${rankings.slice(0, 10).map((score, index) => `
                    <div class="ranking-item">
                        <span class="ranking-position">#${index + 1}</span>
                        <div class="ranking-info">
                            <div class="ranking-name">${getThemeName(score.theme)} - ${getLevelName(score.level)}</div>
                            <div class="ranking-details">${getDifficultyName(score.difficulty)} • ${new Date(score.date).toLocaleDateString()}</div>
                        </div>
                        <span class="ranking-score">${score.percentage}%</span>
                    </div>
                `).join('')}
            </div>
        </div>
    `;

    if (rankings.length === 0) {
        container.innerHTML = '<p style="text-align: center; color: var(--text-muted);">Aucun score enregistré</p>';
    }
}

function updateMultiplayerRankings() {
    const container = document.getElementById('multiplayer-rankings');
    if (!container) return;

    container.innerHTML = '<p style="text-align: center; color: var(--text-muted);">Mode multijoueur à venir !</p>';
}

function updateGlobalRankings() {
    const container = document.getElementById('global-rankings');
    if (!container) return;

    // Combiner tous les scores pour un classement global
    const allScores = gameState.rankings.solo.progress;
    const globalRanking = allScores
        .sort((a, b) => b.percentage - a.percentage)
        .slice(0, 20);

    container.innerHTML = `
        <div class="ranking-category">
            <h4>Classement global</h4>
            <div class="ranking-list">
                ${globalRanking.map((score, index) => `
                    <div class="ranking-item">
                        <span class="ranking-position">#${index + 1}</span>
                        <div class="ranking-info">
                            <div class="ranking-name">${getThemeName(score.theme)}</div>
                            <div class="ranking-details">${getLevelName(score.level)} - ${getDifficultyName(score.difficulty)}</div>
                        </div>
                        <span class="ranking-score">${score.percentage}%</span>
                    </div>
                `).join('')}
            </div>
        </div>
    `;

    if (globalRanking.length === 0) {
        container.innerHTML = '<p style="text-align: center; color: var(--text-muted);">Aucun score enregistré</p>';
    }
}

function updateSidebarRankings() {
    const rankings = gameState.rankings.solo.progress;

    // Calcul du rang de vitesse (basé sur le temps moyen)
    const avgTime = rankings.length > 0
        ? rankings.reduce((sum, score) => sum + score.timeSpent, 0) / rankings.length
        : 0;
    const speedRank = avgTime > 0 ? Math.max(1, Math.ceil(avgTime / 10)) : '-';

    // Calcul du rang de progression (basé sur le pourcentage moyen)
    const avgPercentage = rankings.length > 0
        ? rankings.reduce((sum, score) => sum + score.percentage, 0) / rankings.length
        : 0;
    const progressRank = avgPercentage > 0 ? Math.max(1, Math.ceil((100 - avgPercentage) / 10)) : '-';

    // Calcul du rang hebdomadaire (scores de la semaine)
    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
    const weeklyScores = rankings.filter(score => new Date(score.date) > oneWeekAgo);
    const weeklyRank = weeklyScores.length > 0 ? Math.min(weeklyScores.length, 10) : '-';

    document.getElementById('speed-rank').textContent = speedRank;
    document.getElementById('progress-rank').textContent = progressRank;
    document.getElementById('weekly-rank').textContent = weeklyRank;
}

// Fonctions d'extension pour plus de contenu
function addMoreQuestions() {
    // Cette fonction peut être utilisée pour ajouter plus de questions dynamiquement
    // Par exemple, en chargeant depuis une API ou un fichier JSON
}

function generateRandomQuestion(theme, level, difficulty) {
    // Générateur de questions aléatoires pour les défis quotidiens
    const templates = {
        mathematiques: [
            {
                template: "Combien font {a} + {b} ?",
                generator: () => {
                    const a = Math.floor(Math.random() * 20) + 1;
                    const b = Math.floor(Math.random() * 20) + 1;
                    const correct = a + b;
                    const wrong1 = correct + Math.floor(Math.random() * 5) + 1;
                    const wrong2 = correct - Math.floor(Math.random() * 5) - 1;
                    const wrong3 = correct + Math.floor(Math.random() * 10) + 5;

                    return {
                        question: `Combien font ${a} + ${b} ?`,
                        answers: [correct, wrong1, wrong2, wrong3].sort(() => Math.random() - 0.5),
                        correct: [correct, wrong1, wrong2, wrong3].sort(() => Math.random() - 0.5).indexOf(correct),
                        type: "multiple"
                    };
                }
            }
        ]
    };

    const themeTemplates = templates[theme];
    if (!themeTemplates) return null;

    const template = themeTemplates[Math.floor(Math.random() * themeTemplates.length)];
    return template.generator();
}

// Système de récompenses et achievements
class AchievementSystem {
    static achievements = [
        {
            id: 'first_game',
            name: 'Premier pas',
            description: 'Terminer votre première partie',
            icon: 'fas fa-baby',
            unlocked: false
        },
        {
            id: 'perfect_score',
            name: 'Parfait !',
            description: 'Obtenir 100% à un quiz',
            icon: 'fas fa-star',
            unlocked: false
        },
        {
            id: 'speed_demon',
            name: 'Éclair',
            description: 'Terminer un quiz en moins de 60 secondes',
            icon: 'fas fa-bolt',
            unlocked: false
        },
        {
            id: 'persistent',
            name: 'Persévérant',
            description: 'Jouer 10 parties',
            icon: 'fas fa-medal',
            unlocked: false
        }
    ];

    static checkAchievements(gameData) {
        // Vérifier les achievements après chaque partie
        this.achievements.forEach(achievement => {
            if (!achievement.unlocked) {
                if (this.checkAchievement(achievement.id, gameData)) {
                    this.unlockAchievement(achievement.id);
                }
            }
        });
    }

    static checkAchievement(id, gameData) {
        switch (id) {
            case 'first_game':
                return gameData.gamesPlayed >= 1;
            case 'perfect_score':
                return gameData.percentage === 100;
            case 'speed_demon':
                return gameData.timeSpent <= 60;
            case 'persistent':
                return gameData.gamesPlayed >= 10;
            default:
                return false;
        }
    }

    static unlockAchievement(id) {
        const achievement = this.achievements.find(a => a.id === id);
        if (achievement) {
            achievement.unlocked = true;
            this.showAchievementNotification(achievement);
            this.saveAchievements();
        }
    }

    static showAchievementNotification(achievement) {
        const notification = document.createElement('div');
        notification.className = 'achievement-notification';
        notification.innerHTML = `
            <div class="achievement-content">
                <i class="${achievement.icon}"></i>
                <div>
                    <h4>Achievement débloqué !</h4>
                    <p><strong>${achievement.name}</strong></p>
                    <small>${achievement.description}</small>
                </div>
            </div>
        `;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 1rem;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            z-index: 1002;
            animation: slideInRight 0.5s ease-out;
            max-width: 300px;
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.5s ease-in';
            setTimeout(() => notification.remove(), 500);
        }, 4000);
    }

    static saveAchievements() {
        localStorage.setItem('primevalQuizAchievements', JSON.stringify(this.achievements));
    }

    static loadAchievements() {
        const saved = localStorage.getItem('primevalQuizAchievements');
        if (saved) {
            const savedAchievements = JSON.parse(saved);
            savedAchievements.forEach(saved => {
                const achievement = this.achievements.find(a => a.id === saved.id);
                if (achievement) {
                    achievement.unlocked = saved.unlocked;
                }
            });
        }
    }
}

// Initialiser les achievements au chargement
document.addEventListener('DOMContentLoaded', function() {
    AchievementSystem.loadAchievements();
});

// Ajouter les styles CSS pour les animations d'achievements
const achievementStyles = document.createElement('style');
achievementStyles.textContent = `
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }

    .achievement-content {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .achievement-content i {
        font-size: 2rem;
        color: var(--warning-color);
    }

    .achievement-content h4 {
        margin: 0 0 0.25rem 0;
        font-size: 1rem;
    }

    .achievement-content p {
        margin: 0 0 0.25rem 0;
        font-size: 0.9rem;
    }

    .achievement-content small {
        font-size: 0.8rem;
        opacity: 0.9;
    }
`;
document.head.appendChild(achievementStyles);
