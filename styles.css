/* Variables CSS */
:root {
    --primary-color: #4a90e2;
    --secondary-color: #7b68ee;
    --accent-color: #ff6b6b;
    --success-color: #51cf66;
    --warning-color: #ffd43b;
    --error-color: #ff6b6b;
    --dark-bg: #1a1a2e;
    --darker-bg: #16213e;
    --light-bg: #0f3460;
    --text-light: #ffffff;
    --text-dark: #333333;
    --text-muted: #888888;
    --border-radius: 12px;
    --shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    --transition: all 0.3s ease;
}

/* Mode daltonien */
.colorblind-mode {
    --primary-color: #2196f3;
    --secondary-color: #9c27b0;
    --accent-color: #f44336;
    --success-color: #4caf50;
    --warning-color: #ff9800;
}

/* Reset et base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, var(--dark-bg) 0%, var(--darker-bg) 50%, var(--light-bg) 100%);
    color: var(--text-light);
    overflow-x: hidden;
    min-height: 100vh;
}

/* Arrière-plans dynamiques */
.background-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><radialGradient id="g" cx="50%" cy="50%"><stop offset="0%" stop-color="rgba(255,255,255,0.1)"/><stop offset="100%" stop-color="rgba(255,255,255,0)"/></radialGradient></defs><circle cx="20" cy="20" r="2" fill="url(%23g)"/><circle cx="80" cy="40" r="1.5" fill="url(%23g)"/><circle cx="40" cy="80" r="1" fill="url(%23g)"/><circle cx="90" cy="90" r="2.5" fill="url(%23g)"/></svg>');
    animation: float 20s ease-in-out infinite;
    z-index: -1;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

/* Écrans */
.screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    display: none;
    align-items: center;
    justify-content: center;
    padding: 20px;
    z-index: 1;
}

.screen.active {
    display: flex;
}

/* Écran d'accueil */
.welcome-content {
    text-align: center;
    max-width: 600px;
    width: 100%;
    z-index: 2;
}

.logo-container {
    margin-bottom: 3rem;
}

.game-title {
    font-size: 3.5rem;
    font-weight: 700;
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.5rem;
    text-shadow: 0 0 30px rgba(74, 144, 226, 0.5);
}

.game-subtitle {
    font-size: 1.2rem;
    color: var(--text-muted);
    margin-bottom: 2rem;
}

.difficulty-selection {
    margin-bottom: 3rem;
}

.difficulty-selection h2 {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    color: var(--text-light);
}

.difficulty-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.difficulty-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid transparent;
    border-radius: var(--border-radius);
    padding: 1.5rem 2rem;
    color: var(--text-light);
    cursor: pointer;
    transition: var(--transition);
    backdrop-filter: blur(10px);
    min-width: 150px;
}

.difficulty-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.difficulty-btn.selected {
    border-color: var(--primary-color);
    background: rgba(74, 144, 226, 0.2);
}

.difficulty-btn i {
    display: block;
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.main-btn {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    border: none;
    border-radius: var(--border-radius);
    padding: 1rem 3rem;
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-light);
    cursor: pointer;
    transition: var(--transition);
    box-shadow: var(--shadow);
}

.main-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 25px rgba(74, 144, 226, 0.4);
}

.main-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.main-btn i {
    margin-right: 0.5rem;
}

/* Menu principal */
.menu-content {
    max-width: 1200px;
    width: 100%;
    z-index: 2;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 3rem;
}

.header .game-title {
    font-size: 2.5rem;
}

.user-stats {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.level-indicator {
    background: rgba(255, 255, 255, 0.1);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    backdrop-filter: blur(10px);
}

.menu-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    margin-bottom: 3rem;
}

.menu-section h2 {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    color: var(--primary-color);
}

.menu-buttons {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.menu-btn {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    color: var(--text-light);
    cursor: pointer;
    transition: var(--transition);
    backdrop-filter: blur(10px);
    text-align: left;
}

.menu-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.menu-btn i {
    font-size: 1.5rem;
    margin-right: 1rem;
    color: var(--primary-color);
}

.menu-btn span {
    display: block;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.menu-btn small {
    color: var(--text-muted);
    font-size: 0.9rem;
}

.theme-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.theme-btn {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    border-radius: var(--border-radius);
    padding: 1.5rem 1rem;
    color: var(--text-light);
    cursor: pointer;
    transition: var(--transition);
    backdrop-filter: blur(10px);
    text-align: center;
}

.theme-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.theme-btn i {
    display: block;
    font-size: 2rem;
    margin-bottom: 0.5rem;
    color: var(--secondary-color);
}

.theme-btn span {
    font-size: 0.9rem;
    font-weight: 500;
}

.bottom-menu {
    display: flex;
    justify-content: center;
    gap: 2rem;
}

.icon-btn {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    border-radius: var(--border-radius);
    padding: 1rem;
    color: var(--text-light);
    cursor: pointer;
    transition: var(--transition);
    backdrop-filter: blur(10px);
    text-align: center;
    min-width: 100px;
}

.icon-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.icon-btn i {
    display: block;
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.icon-btn span {
    font-size: 0.8rem;
}

/* Classement latéral */
.sidebar-ranking {
    position: fixed;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    width: 200px;
    z-index: 3;
}

.sidebar-ranking h3 {
    font-size: 1rem;
    margin-bottom: 1rem;
    text-align: center;
    color: var(--primary-color);
}

.ranking-stats {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 6px;
}

.stat-label {
    font-size: 0.8rem;
    color: var(--text-muted);
}

.stat-value {
    font-weight: 600;
    color: var(--text-light);
}

/* Écran de jeu */
.game-content {
    max-width: 800px;
    width: 100%;
    z-index: 2;
}

.game-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius);
    padding: 1rem 1.5rem;
}

.game-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.theme-name {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--primary-color);
}

.level-name {
    font-size: 0.9rem;
    color: var(--text-muted);
}

.game-stats {
    display: flex;
    gap: 1.5rem;
    align-items: center;
}

.game-stats > div {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
}

.timer i { color: var(--warning-color); }
.score i { color: var(--success-color); }
.errors i { color: var(--error-color); }

.question-container {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius);
    padding: 2rem;
    margin-bottom: 2rem;
    text-align: center;
}

.question-number {
    font-size: 0.9rem;
    color: var(--text-muted);
    margin-bottom: 1rem;
}

.question-text {
    font-size: 1.3rem;
    font-weight: 500;
    line-height: 1.5;
    margin-bottom: 1rem;
}

.question-image {
    margin-top: 1rem;
}

.question-image img {
    max-width: 100%;
    max-height: 300px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.answers-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.answer-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid transparent;
    border-radius: var(--border-radius);
    padding: 1rem 1.5rem;
    color: var(--text-light);
    cursor: pointer;
    transition: var(--transition);
    backdrop-filter: blur(10px);
    text-align: left;
    font-size: 1rem;
}

.answer-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.answer-btn.selected {
    border-color: var(--primary-color);
    background: rgba(74, 144, 226, 0.2);
}

.answer-btn.correct {
    border-color: var(--success-color);
    background: rgba(81, 207, 102, 0.2);
}

.answer-btn.incorrect {
    border-color: var(--error-color);
    background: rgba(255, 107, 107, 0.2);
}

.game-controls {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

.control-btn {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    border-radius: var(--border-radius);
    padding: 0.75rem 1.5rem;
    color: var(--text-light);
    cursor: pointer;
    transition: var(--transition);
    backdrop-filter: blur(10px);
}

.control-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.control-btn i {
    margin-right: 0.5rem;
}

/* Écrans de paramètres, classements et crédits */
.settings-content,
.rankings-content,
.credits-content {
    max-width: 800px;
    width: 100%;
    z-index: 2;
}

.settings-header,
.rankings-header,
.credits-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2rem;
}

.back-btn {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    color: var(--text-light);
    cursor: pointer;
    transition: var(--transition);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
}

.back-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.settings-sections {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.settings-section {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius);
    padding: 2rem;
}

.settings-section h3 {
    font-size: 1.3rem;
    margin-bottom: 1.5rem;
    color: var(--primary-color);
}

.setting-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
    padding: 0.75rem 0;
}

.setting-item label {
    font-weight: 500;
    color: var(--text-light);
}

.setting-item input[type="range"] {
    width: 150px;
    margin: 0 1rem;
}

.setting-item select {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    padding: 0.5rem;
    color: var(--text-light);
    backdrop-filter: blur(10px);
}

.setting-item input[type="checkbox"] {
    width: 20px;
    height: 20px;
    accent-color: var(--primary-color);
}

.volume-value {
    min-width: 40px;
    text-align: right;
    font-weight: 600;
    color: var(--primary-color);
}

/* Classements */
.rankings-tabs {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    justify-content: center;
}

.tab-btn {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    border-radius: var(--border-radius);
    padding: 0.75rem 1.5rem;
    color: var(--text-light);
    cursor: pointer;
    transition: var(--transition);
    backdrop-filter: blur(10px);
}

.tab-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.tab-btn.active {
    background: var(--primary-color);
    color: white;
}

.rankings-content-area {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius);
    padding: 2rem;
    min-height: 400px;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.ranking-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.ranking-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    transition: var(--transition);
}

.ranking-item:hover {
    background: rgba(255, 255, 255, 0.1);
}

.ranking-position {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--primary-color);
    min-width: 40px;
}

.ranking-info {
    flex: 1;
    margin-left: 1rem;
}

.ranking-name {
    font-weight: 600;
    color: var(--text-light);
}

.ranking-details {
    font-size: 0.9rem;
    color: var(--text-muted);
}

.ranking-score {
    font-weight: 700;
    color: var(--success-color);
}

/* Crédits */
.credits-info {
    text-align: center;
}

.badge-honor {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius);
    padding: 3rem 2rem;
    margin-bottom: 2rem;
}

.badge-honor i {
    font-size: 4rem;
    color: var(--warning-color);
    margin-bottom: 1rem;
}

.badge-honor h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.team-info {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius);
    padding: 2rem;
}

.team-info h4 {
    font-size: 1.2rem;
    margin-bottom: 1rem;
    color: var(--secondary-color);
}

/* Modales */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.modal-overlay.active {
    display: flex;
}

.modal {
    background: var(--dark-bg);
    border-radius: var(--border-radius);
    padding: 2rem;
    max-width: 500px;
    width: 90%;
    box-shadow: var(--shadow);
    transform: scale(0.8);
    transition: var(--transition);
}

.modal-overlay.active .modal {
    transform: scale(1);
}

.modal-content {
    text-align: center;
}

.modal-content h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.modal-content p {
    margin-bottom: 2rem;
    color: var(--text-light);
    line-height: 1.5;
}

.modal-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.modal-btn {
    background: var(--primary-color);
    border: none;
    border-radius: var(--border-radius);
    padding: 0.75rem 1.5rem;
    color: white;
    cursor: pointer;
    transition: var(--transition);
    font-weight: 600;
}

.modal-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(74, 144, 226, 0.4);
}

.modal-btn.secondary {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-light);
}

.modal-btn.secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow);
}

/* Animations */
@keyframes slideInUp {
    from {
        transform: translateY(100px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes slideInDown {
    from {
        transform: translateY(-100px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.screen.active {
    animation: fadeIn 0.5s ease-in-out;
}

/* Responsive */
@media (max-width: 768px) {
    .game-title {
        font-size: 2.5rem;
    }

    .menu-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .difficulty-buttons {
        flex-direction: column;
        align-items: center;
    }

    .difficulty-btn {
        width: 100%;
        max-width: 300px;
    }

    .sidebar-ranking {
        position: static;
        transform: none;
        width: 100%;
        margin-top: 2rem;
    }

    .game-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .game-stats {
        justify-content: center;
    }

    .answers-container {
        grid-template-columns: 1fr;
    }

    .bottom-menu {
        flex-wrap: wrap;
        gap: 1rem;
    }

    .rankings-tabs {
        flex-wrap: wrap;
    }

    .setting-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .setting-item input[type="range"] {
        width: 100%;
        margin: 0;
    }

    .modal-buttons {
        flex-direction: column;
    }
}

/* Styles pour les nouvelles fonctionnalités */
/* Animations pour level up */
@keyframes levelUpAnimation {
    0% {
        transform: translate(-50%, -50%) scale(0.5);
        opacity: 0;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.1);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }
}

.level-up-content {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.level-up-content i {
    font-size: 3rem;
    color: var(--warning-color);
}

.level-up-content h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.3rem;
}

.level-up-content p {
    margin: 0 0 0.25rem 0;
    font-size: 1rem;
}

.level-up-content small {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* Modal de sélection de niveau */
.level-selection-modal .modal {
    max-width: 600px;
}

.level-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    margin: 2rem 0;
}

.level-option {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid transparent;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    color: var(--text-light);
    cursor: pointer;
    transition: var(--transition);
    backdrop-filter: blur(10px);
    text-align: center;
}

.level-option:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.level-option.selected {
    border-color: var(--primary-color);
    background: rgba(74, 144, 226, 0.2);
}

.level-option.current {
    border-color: var(--success-color);
    background: rgba(81, 207, 102, 0.2);
}

.level-option i {
    display: block;
    font-size: 2rem;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

.level-option span {
    display: block;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.level-option small {
    color: var(--text-muted);
    font-size: 0.8rem;
}

/* Indicateur de niveau cliquable */
.level-indicator:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

/* Styles pour les achievements */
.achievement-content {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.achievement-content i {
    font-size: 2rem;
    color: var(--warning-color);
}

.achievement-content h4 {
    margin: 0 0 0.25rem 0;
    font-size: 1rem;
}

.achievement-content p {
    margin: 0 0 0.25rem 0;
    font-size: 0.9rem;
}

.achievement-content small {
    font-size: 0.8rem;
    opacity: 0.9;
}

/* Responsive pour les nouvelles fonctionnalités */
@media (max-width: 768px) {
    .level-grid {
        grid-template-columns: 1fr;
    }

    .level-up-content {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .achievement-content {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }
}

/* Styles pour les résultats détaillés */
.result-stats {
    text-align: left;
    margin: 1rem 0;
}

.stat-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-row:last-child {
    border-bottom: none;
}

.stat-label {
    color: var(--text-muted);
    font-weight: 500;
}

.stat-value {
    color: var(--text-light);
    font-weight: 600;
}

/* Notifications d'erreur */
.error-content {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.error-content i {
    font-size: 1.2rem;
}

/* Animations supplémentaires */
@keyframes slideOutUp {
    from {
        transform: translateX(-50%) translateY(0);
        opacity: 1;
    }
    to {
        transform: translateX(-50%) translateY(-100px);
        opacity: 0;
    }
}

/* Amélioration de l'animation slideInUp */
@keyframes slideInUp {
    from {
        transform: translateY(50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Styles pour les questions avec images */
.question-image img {
    max-width: 100%;
    max-height: 250px;
    object-fit: contain;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.question-image img:hover {
    transform: scale(1.05);
}

/* Amélioration des boutons de réponse */
.answer-btn {
    position: relative;
    overflow: hidden;
}

.answer-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.answer-btn:hover::before {
    left: 100%;
}

/* Indicateur de progression */
.progress-indicator {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: rgba(255, 255, 255, 0.1);
    z-index: 1000;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    width: 0%;
    transition: width 0.3s ease;
}

/* Amélioration du feedback visuel */
.feedback {
    animation: feedbackPulse 0.6s ease-out;
}

@keyframes feedbackPulse {
    0% {
        transform: translate(-50%, -50%) scale(0.8);
        opacity: 0;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.1);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }
}

/* Mode sombre automatique */
@media (prefers-color-scheme: dark) {
    :root {
        --dark-bg: #0a0a0a;
        --darker-bg: #1a1a1a;
        --light-bg: #2a2a2a;
    }
}

/* Amélioration de l'accessibilité */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Focus visible pour l'accessibilité */
button:focus-visible,
input:focus-visible,
select:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Amélioration pour les écrans très petits */
@media (max-width: 480px) {
    .game-title {
        font-size: 2rem;
    }

    .difficulty-btn,
    .menu-btn,
    .theme-btn {
        padding: 1rem;
    }

    .game-header {
        padding: 0.75rem;
    }

    .question-container {
        padding: 1.5rem;
    }

    .modal {
        margin: 1rem;
        padding: 1.5rem;
    }
}
