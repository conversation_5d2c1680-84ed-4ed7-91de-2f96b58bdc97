<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Générateur de Musique Électro - Primeval Quiz</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #1a1a2e, #16213e, #0f3460);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }
        h1 {
            color: #4a90e2;
            margin-bottom: 2rem;
        }
        .music-controls {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 2rem;
            margin: 2rem 0;
            backdrop-filter: blur(10px);
        }
        button {
            background: linear-gradient(45deg, #4a90e2, #7b68ee);
            border: none;
            border-radius: 8px;
            color: white;
            padding: 1rem 2rem;
            margin: 0.5rem;
            cursor: pointer;
            font-size: 1rem;
            transition: transform 0.2s;
        }
        button:hover {
            transform: translateY(-2px);
        }
        button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        .info {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            text-align: left;
        }
        .waveform {
            width: 100%;
            height: 100px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            margin: 1rem 0;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }
        .wave-bar {
            width: 4px;
            background: #4a90e2;
            margin: 0 1px;
            border-radius: 2px;
            transition: height 0.1s;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎵 Générateur de Musique Électro</h1>
        <p>Créez votre propre musique électro pour Primeval Quiz !</p>
        
        <div class="music-controls">
            <h3>Contrôles Audio</h3>
            <button id="playWelcome">🎵 Jouer Musique d'Accueil</button>
            <button id="playMenu">🎶 Jouer Musique de Menu</button>
            <button id="playGame">🎮 Jouer Musique de Jeu</button>
            <button id="stopAll">⏹️ Arrêter Tout</button>
            
            <div class="waveform" id="waveform">
                <div class="wave-bar" style="height: 20px;"></div>
                <div class="wave-bar" style="height: 40px;"></div>
                <div class="wave-bar" style="height: 60px;"></div>
                <div class="wave-bar" style="height: 30px;"></div>
                <div class="wave-bar" style="height: 80px;"></div>
                <div class="wave-bar" style="height: 50px;"></div>
                <div class="wave-bar" style="height: 70px;"></div>
                <div class="wave-bar" style="height: 35px;"></div>
                <div class="wave-bar" style="height: 45px;"></div>
                <div class="wave-bar" style="height: 65px;"></div>
            </div>
            
            <div>
                <label>Volume: </label>
                <input type="range" id="volume" min="0" max="100" value="50">
                <span id="volumeValue">50%</span>
            </div>
        </div>
        
        <div class="info">
            <h3>🎼 Générateur de Musique Web Audio</h3>
            <p>Ce générateur utilise l'API Web Audio pour créer de la musique électro en temps réel :</p>
            <ul>
                <li><strong>Musique d'Accueil</strong> : Ambiance douce et accueillante</li>
                <li><strong>Musique de Menu</strong> : Rythme modéré et motivant</li>
                <li><strong>Musique de Jeu</strong> : Énergique pour maintenir la concentration</li>
            </ul>
        </div>
        
        <div class="info">
            <h3>💾 Comment sauvegarder</h3>
            <p>Pour utiliser cette musique dans votre jeu :</p>
            <ol>
                <li>Utilisez un logiciel d'enregistrement audio (Audacity, OBS, etc.)</li>
                <li>Enregistrez la sortie audio de votre navigateur</li>
                <li>Sauvegardez en MP3 avec les noms requis</li>
                <li>Placez les fichiers dans le dossier audio/ du jeu</li>
            </ol>
        </div>
    </div>

    <script>
        class ElectroMusicGenerator {
            constructor() {
                this.audioContext = null;
                this.currentTrack = null;
                this.volume = 0.5;
                this.isPlaying = false;
                
                this.initAudio();
                this.setupControls();
            }
            
            initAudio() {
                try {
                    this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
                    this.masterGain = this.audioContext.createGain();
                    this.masterGain.connect(this.audioContext.destination);
                    this.masterGain.gain.value = this.volume;
                } catch (e) {
                    console.error('Web Audio API non supportée:', e);
                }
            }
            
            setupControls() {
                document.getElementById('playWelcome').addEventListener('click', () => this.playWelcomeMusic());
                document.getElementById('playMenu').addEventListener('click', () => this.playMenuMusic());
                document.getElementById('playGame').addEventListener('click', () => this.playGameMusic());
                document.getElementById('stopAll').addEventListener('click', () => this.stopAll());
                
                const volumeSlider = document.getElementById('volume');
                volumeSlider.addEventListener('input', (e) => {
                    this.volume = e.target.value / 100;
                    if (this.masterGain) {
                        this.masterGain.gain.value = this.volume;
                    }
                    document.getElementById('volumeValue').textContent = e.target.value + '%';
                });
            }
            
            createOscillator(frequency, type = 'sine') {
                const osc = this.audioContext.createOscillator();
                osc.frequency.value = frequency;
                osc.type = type;
                return osc;
            }
            
            createEnvelope(gain, attack = 0.1, decay = 0.1, sustain = 0.7, release = 0.5) {
                const now = this.audioContext.currentTime;
                gain.gain.setValueAtTime(0, now);
                gain.gain.linearRampToValueAtTime(1, now + attack);
                gain.gain.linearRampToValueAtTime(sustain, now + attack + decay);
                return now + attack + decay;
            }
            
            playWelcomeMusic() {
                this.stopAll();
                this.isPlaying = true;
                
                // Ambiance douce avec pads
                const pad1 = this.createOscillator(220, 'sawtooth');
                const pad2 = this.createOscillator(330, 'sawtooth');
                const pad3 = this.createOscillator(440, 'sine');
                
                const gain1 = this.audioContext.createGain();
                const gain2 = this.audioContext.createGain();
                const gain3 = this.audioContext.createGain();
                
                pad1.connect(gain1);
                pad2.connect(gain2);
                pad3.connect(gain3);
                
                gain1.connect(this.masterGain);
                gain2.connect(this.masterGain);
                gain3.connect(this.masterGain);
                
                gain1.gain.value = 0.1;
                gain2.gain.value = 0.08;
                gain3.gain.value = 0.15;
                
                pad1.start();
                pad2.start();
                pad3.start();
                
                this.currentTrack = [pad1, pad2, pad3];
                this.animateWaveform();
                
                // Arrêter après 30 secondes pour la démo
                setTimeout(() => this.stopAll(), 30000);
            }
            
            playMenuMusic() {
                this.stopAll();
                this.isPlaying = true;
                
                // Rythme électro avec basse
                const bass = this.createOscillator(110, 'sawtooth');
                const lead = this.createOscillator(440, 'square');
                const arp = this.createOscillator(880, 'sine');
                
                const bassGain = this.audioContext.createGain();
                const leadGain = this.audioContext.createGain();
                const arpGain = this.audioContext.createGain();
                
                bass.connect(bassGain);
                lead.connect(leadGain);
                arp.connect(arpGain);
                
                bassGain.connect(this.masterGain);
                leadGain.connect(this.masterGain);
                arpGain.connect(this.masterGain);
                
                bassGain.gain.value = 0.2;
                leadGain.gain.value = 0.1;
                arpGain.gain.value = 0.08;
                
                bass.start();
                lead.start();
                arp.start();
                
                this.currentTrack = [bass, lead, arp];
                this.animateWaveform();
                
                setTimeout(() => this.stopAll(), 30000);
            }
            
            playGameMusic() {
                this.stopAll();
                this.isPlaying = true;
                
                // Musique énergique pour le jeu
                const kick = this.createOscillator(60, 'sine');
                const synth = this.createOscillator(523, 'sawtooth');
                const hihat = this.createOscillator(8000, 'square');
                
                const kickGain = this.audioContext.createGain();
                const synthGain = this.audioContext.createGain();
                const hihatGain = this.audioContext.createGain();
                
                kick.connect(kickGain);
                synth.connect(synthGain);
                hihat.connect(hihatGain);
                
                kickGain.connect(this.masterGain);
                synthGain.connect(this.masterGain);
                hihatGain.connect(this.masterGain);
                
                kickGain.gain.value = 0.3;
                synthGain.gain.value = 0.15;
                hihatGain.gain.value = 0.05;
                
                kick.start();
                synth.start();
                hihat.start();
                
                this.currentTrack = [kick, synth, hihat];
                this.animateWaveform();
                
                setTimeout(() => this.stopAll(), 30000);
            }
            
            stopAll() {
                if (this.currentTrack) {
                    this.currentTrack.forEach(osc => {
                        try {
                            osc.stop();
                        } catch (e) {
                            // Oscillateur déjà arrêté
                        }
                    });
                    this.currentTrack = null;
                }
                this.isPlaying = false;
                this.stopWaveformAnimation();
            }
            
            animateWaveform() {
                if (!this.isPlaying) return;
                
                const bars = document.querySelectorAll('.wave-bar');
                bars.forEach(bar => {
                    const height = Math.random() * 80 + 10;
                    bar.style.height = height + 'px';
                    bar.style.background = `hsl(${200 + Math.random() * 60}, 70%, 60%)`;
                });
                
                setTimeout(() => this.animateWaveform(), 100);
            }
            
            stopWaveformAnimation() {
                const bars = document.querySelectorAll('.wave-bar');
                bars.forEach(bar => {
                    bar.style.height = '20px';
                    bar.style.background = '#4a90e2';
                });
            }
        }
        
        // Initialiser le générateur au chargement
        document.addEventListener('DOMContentLoaded', () => {
            const generator = new ElectroMusicGenerator();
        });
    </script>
</body>
</html>
